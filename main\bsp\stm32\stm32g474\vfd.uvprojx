<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>发行运营</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>6160000::V6.16::ARMCLANG</pCCUsed>
      <uAC6>1</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>STM32G474RETx</Device>
          <Vendor>STMicroelectronics</Vendor>
          <PackID>Keil.STM32G4xx_DFP.1.2.0</PackID>
          <PackURL>http://www.keil.com/pack/</PackURL>
          <Cpu>IRAM(0x20000000,0x00020000) IROM(0x08000000,0x00080000) CPUTYPE("Cortex-M4") FPU2 CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN1 -FF0STM32G4xx_512_Dual -********** -FL080000 -FP0($$Device:STM32G474RETx$CMSIS\Flash\STM32G4xx_512_Dual.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:STM32G474RETx$Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:STM32G474RETx$CMSIS\SVD\STM32G474xx.svd</SFDFile>
          <bCustSvd>1</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>..\..\..\..\build\inv\</OutputDirectory>
          <OutputName>PMSM12_APP_A01</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>..\..\..\..\build\inv\list\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>1</RunUserProg2>
            <UserProg1Name>fromelf --bin !L --output ../../../../build/bin/@H.bin</UserProg1Name>
            <UserProg2Name>python BIN_HEX_PY.py -c BIN_HEX_CONFIG.xml</UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> -REMAP -MPU</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> -MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3></Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>0</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x80000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x80000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>2</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>1</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>3</v6Lang>
            <v6LangP>5</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>HW_PVPB08,NOVFD_TEST_DEBUG,NOUSE_MCSDK_UART,ICS_SENSORS,USE_HAL_DRIVER, LFS_CONFIG=lfs_config.h, __RTTHREAD__, STM32G474xx, __CLK_TCK=RT_TICK_PER_SECOND,ARM_MATH_CM4</Define>
              <Undefine></Undefine>
              <IncludePath>applications;.;..\..\..\components\net\at\include;..\..\..\components\net\at\at_socket;packages\at_device-latest\inc;packages\at_device-latest\class\esp8266;..\..\..\libcpu\arm\common;..\..\..\libcpu\arm\cortex-m4;..\..\..\components\drivers\hwcrypto;.;..\..\..\components\drivers\include;..\..\..\components\drivers\include;..\..\..\components\drivers\include;..\..\..\components\drivers\include;..\..\..\components\drivers\spi;..\..\..\components\drivers\include;..\..\..\components\drivers\spi\sfud\inc;..\..\..\components\drivers\include;..\..\..\components\drivers\include;board;board\CubeMX_Config\Inc;..\libraries\HAL_Drivers;..\libraries\HAL_Drivers\config;packages\fal-v0.5.0\inc;..\..\..\components\dfs\include;..\..\..\components\dfs\filesystems\devfs;..\..\..\components\finsh;.;..\..\..\include;..\..\..\components\libc\compilers\common;..\..\..\components\libc\compilers\common\nogcc;..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Inc;..\libraries\STM32G4xx_HAL\CMSIS\Device\ST\STM32G4xx\Include;..\libraries\STM32G4xx_HAL\CMSIS\Include;packages\littlefs-latest;packages\netutils-v1.3.1\ntp;..\..\..\components\net\netdev\include;..\..\..\components\net\sal_socket\include;..\..\..\components\net\sal_socket\include\socket;..\..\..\components\net\sal_socket\impl;..\..\..\components\net\sal_socket\include\socket\sys_socket;..\..\..\examples\utest\testcases\kernel;..\..\..\components\utilities\ulog;.\packages\FlashDB-v1.1.0\inc;packages\freemodbus-latest\modbus\include;packages\freemodbus-latest\modbus\rtu;packages\freemodbus-latest\modbus\ascii;packages\freemodbus-latest\modbus\tcp;packages\freemodbus-latest\port;.\BsmProtocol;.\CRC;..\libraries\STM32G4xx_HAL\CMSIS\DSP\Include;.\user\inc;.\framework_foc\acim_svpwm;.\framework_pfc;.\framework_boost;.\bsmcomm_protocol;.\customer_port\merak;..\..\..\components\drivers\include\drivers;.\packages\lis3dh;.\framework_foc\pmsm_inovance\Core\Inc;.\framework_foc\pmsm_inovance\Core\Inc\INC_USER;.\framework_foc\pmsm_inovance\Core\Src\userApps;.\framework_foc\pmsm_inovance;.\packages\lis3dh</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>4</ClangAsOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>0</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x08000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile>.\board\linker_scripts\link.sct</ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>customer/yingsai</GroupName>
          <Files>
            <File>
              <FileName>customer_port.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\customer_port\merak\customer_port.h</FilePath>
            </File>
            <File>
              <FileName>app_can_port.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\customer_port\merak\app_can_port.c</FilePath>
            </File>
            <File>
              <FileName>app_rs485_port.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\customer_port\merak\app_rs485_port.c</FilePath>
            </File>
            <File>
              <FileName>customer_port.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\customer_port\merak\customer_port.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>user</GroupName>
          <GroupOption>
            <CommonProperty>
              <UseCPPCompiler>0</UseCPPCompiler>
              <RVCTCodeConst>0</RVCTCodeConst>
              <RVCTZI>0</RVCTZI>
              <RVCTOtherData>0</RVCTOtherData>
              <ModuleSelection>0</ModuleSelection>
              <IncludeInBuild>1</IncludeInBuild>
              <AlwaysBuild>0</AlwaysBuild>
              <GenerateAssemblyFile>0</GenerateAssemblyFile>
              <AssembleAssemblyFile>0</AssembleAssemblyFile>
              <PublicsOnly>0</PublicsOnly>
              <StopOnExitCode>3</StopOnExitCode>
              <CustomArgument></CustomArgument>
              <IncludeLibraryModules></IncludeLibraryModules>
              <ComprImg>0</ComprImg>
            </CommonProperty>
            <GroupArmAds>
              <Cads>
                <interw>2</interw>
                <Optim>0</Optim>
                <oTime>2</oTime>
                <SplitLS>2</SplitLS>
                <OneElfS>2</OneElfS>
                <Strict>2</Strict>
                <EnumInt>2</EnumInt>
                <PlainCh>2</PlainCh>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <wLevel>0</wLevel>
                <uThumb>2</uThumb>
                <uSurpInc>2</uSurpInc>
                <uC99>2</uC99>
                <uGnu>2</uGnu>
                <useXO>2</useXO>
                <v6Lang>0</v6Lang>
                <v6LangP>0</v6LangP>
                <vShortEn>2</vShortEn>
                <vShortWch>2</vShortWch>
                <v6Lto>2</v6Lto>
                <v6WtE>2</v6WtE>
                <v6Rtti>2</v6Rtti>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define> </Define>
                  <Undefine> </Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Cads>
              <Aads>
                <interw>2</interw>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <thumb>2</thumb>
                <SplitLS>2</SplitLS>
                <SwStkChk>2</SwStkChk>
                <NoWarn>2</NoWarn>
                <uSurpInc>2</uSurpInc>
                <useXO>2</useXO>
                <ClangAsOpt>0</ClangAsOpt>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Aads>
            </GroupArmAds>
          </GroupOption>
          <Files>
            <File>
              <FileName>uapp.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\user\inc\uapp.h</FilePath>
            </File>
            <File>
              <FileName>app_user.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_user.c</FilePath>
            </File>
            <File>
              <FileName>app_sub.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_sub.c</FilePath>
            </File>
            <File>
              <FileName>app_main.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_main.c</FilePath>
            </File>
            <File>
              <FileName>alg_diag.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\alg_diag.c</FilePath>
            </File>
            <File>
              <FileName>app_diag.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_diag.c</FilePath>
            </File>
            <File>
              <FileName>drv_io.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\drv_io.c</FilePath>
            </File>
            <File>
              <FileName>drv_hal_main.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\drv_hal_main.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_msp.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\stm32g4xx_hal_msp.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_it.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\stm32g4xx_it.c</FilePath>
            </File>
            <File>
              <FileName>alg_dio_edge.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\alg_dio_edge.c</FilePath>
            </File>
            <File>
              <FileName>alg_pcmaster.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\alg_pcmaster.c</FilePath>
            </File>
            <File>
              <FileName>alg_toolbox.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\alg_toolbox.c</FilePath>
            </File>
            <File>
              <FileName>alg_uapp.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\alg_uapp.c</FilePath>
            </File>
            <File>
              <FileName>app_cmd.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_cmd.c</FilePath>
            </File>
            <File>
              <FileName>app_pcmaster.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_pcmaster.c</FilePath>
            </File>
            <File>
              <FileName>app_record.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_record.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>2</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>1</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <uGnu>2</uGnu>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>2</vShortEn>
                    <vShortWch>2</vShortWch>
                    <v6Lto>2</v6Lto>
                    <v6WtE>2</v6WtE>
                    <v6Rtti>2</v6Rtti>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>app_test.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_test.c</FilePath>
            </File>
            <File>
              <FileName>drv_fal_flash_stm32g4_port.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\drv_fal_flash_stm32g4_port.c</FilePath>
            </File>
            <File>
              <FileName>drv_rs485.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\drv_rs485.c</FilePath>
            </File>
            <File>
              <FileName>drv_fdcan.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\drv_fdcan.c</FilePath>
            </File>
            <File>
              <FileName>alg_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\alg_crc.c</FilePath>
            </File>
            <File>
              <FileName>alg_modbus_common.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\alg_modbus_common.c</FilePath>
            </File>
            <File>
              <FileName>app_ad.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_ad.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>2</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>1</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <uGnu>2</uGnu>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>2</vShortEn>
                    <vShortWch>2</vShortWch>
                    <v6Lto>2</v6Lto>
                    <v6WtE>2</v6WtE>
                    <v6Rtti>2</v6Rtti>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>alg_ntc_convert.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\alg_ntc_convert.c</FilePath>
            </File>
            <File>
              <FileName>app_alternate_comm.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_alternate_comm.c</FilePath>
            </File>
            <File>
              <FileName>drv_soft_iic_hdc1080.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\drv_soft_iic_hdc1080.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>2</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>1</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <uGnu>2</uGnu>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>2</vShortEn>
                    <vShortWch>2</vShortWch>
                    <v6Lto>2</v6Lto>
                    <v6WtE>2</v6WtE>
                    <v6Rtti>2</v6Rtti>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>app_dpll3p.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_dpll3p.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>foc/inovance</GroupName>
          <Files>
            <File>
              <FileName>user_mcsdk_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\user_mcsdk_task.c</FilePath>
            </File>
            <File>
              <FileName>arm_abs_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_abs_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_abs_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_abs_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_common_tables.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_common_tables.c</FilePath>
            </File>
            <File>
              <FileName>arm_cos_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_cos_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cos_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_cos_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cos_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_cos_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_float_to_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_float_to_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_q15_to_float.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_q15_to_float.c</FilePath>
            </File>
            <File>
              <FileName>arm_q15_to_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_q15_to_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_q31_to_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_q31_to_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_cos_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_sin_cos_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_cos_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_sin_cos_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_sin_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_sin_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_sin_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_sqrt_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_sqrt_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_sqrt_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_sqrt_q31.c</FilePath>
            </File>
            <File>
              <FileName>DeviceInit.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\DeviceInit.c</FilePath>
            </File>
            <File>
              <FileName>f_comm.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_comm.c</FilePath>
            </File>
            <File>
              <FileName>f_error.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_error.c</FilePath>
            </File>
            <File>
              <FileName>f_frqSrc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_frqSrc.c</FilePath>
            </File>
            <File>
              <FileName>f_funcCode.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_funcCode.c</FilePath>
            </File>
            <File>
              <FileName>f_interface.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_interface.c</FilePath>
            </File>
            <File>
              <FileName>f_main.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_main.c</FilePath>
            </File>
            <File>
              <FileName>f_runSrc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_runSrc.c</FilePath>
            </File>
            <File>
              <FileName>f_runSrc_accDecFrq.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_runSrc_accDecFrq.c</FilePath>
            </File>
            <File>
              <FileName>MotorCarrier.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorCarrier.c</FilePath>
            </File>
            <File>
              <FileName>MotorConst.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorConst.c</FilePath>
            </File>
            <File>
              <FileName>MotorCurrentTransform.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorCurrentTransform.c</FilePath>
            </File>
            <File>
              <FileName>MotorDataExchange.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorDataExchange.c</FilePath>
            </File>
            <File>
              <FileName>MotorEncoder.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorEncoder.c</FilePath>
            </File>
            <File>
              <FileName>MotorInfoCollect.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorInfoCollect.c</FilePath>
            </File>
            <File>
              <FileName>MotorInvProtect.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorInvProtect.c</FilePath>
            </File>
            <File>
              <FileName>MotorIPMSVC.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorIPMSVC.c</FilePath>
            </File>
            <File>
              <FileName>MotorMain.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorMain.c</FilePath>
            </File>
            <File>
              <FileName>MotorParChange.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorParChange.c</FilePath>
            </File>
            <File>
              <FileName>MotorParEst.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorParEst.c</FilePath>
            </File>
            <File>
              <FileName>MotorPmsmMain.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorPmsmMain.c</FilePath>
            </File>
            <File>
              <FileName>MotorPmsmParEst.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorPmsmParEst.c</FilePath>
            </File>
            <File>
              <FileName>MotorPublicCal.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorPublicCal.c</FilePath>
            </File>
            <File>
              <FileName>MotorPWM.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorPWM.c</FilePath>
            </File>
            <File>
              <FileName>MotorSpeed.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorSpeed.c</FilePath>
            </File>
            <File>
              <FileName>MotorVar.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorVar.c</FilePath>
            </File>
            <File>
              <FileName>MotorVCMain.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorVCMain.c</FilePath>
            </File>
            <File>
              <FileName>MotorVF.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorVF.c</FilePath>
            </File>
            <File>
              <FileName>Scope.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\Scope.c</FilePath>
            </File>
            <File>
              <FileName>SubPrg.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\SubPrg.c</FilePath>
            </File>
            <File>
              <FileName>Svpwm.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\Svpwm.c</FilePath>
            </File>
            <File>
              <FileName>testApp.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\testApp.c</FilePath>
            </File>
            <File>
              <FileName>MotorFlyingStart.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorFlyingStart.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>protocol_comm</GroupName>
          <Files>
            <File>
              <FileName>app_bsm_com_outernet.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsmcomm_protocol\app_bsm_com_outernet.c</FilePath>
            </File>
            <File>
              <FileName>api_bsm_com_protocol.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsmcomm_protocol\api_bsm_com_protocol.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Drivers</GroupName>
          <Files>
            <File>
              <FileName>rtconfig.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\rtconfig.h</FilePath>
            </File>
            <File>
              <FileName>startup_stm32g474xx.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\CMSIS\Device\ST\STM32G4xx\Source\Templates\arm\startup_stm32g474xx.s</FilePath>
            </File>
            <File>
              <FileName>board.c</FileName>
              <FileType>1</FileType>
              <FilePath>board\board.c</FilePath>
            </File>
            <File>
              <FileName>drv_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\HAL_Drivers\drv_gpio.c</FilePath>
            </File>
            <File>
              <FileName>drv_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\HAL_Drivers\drv_spi.c</FilePath>
            </File>
            <File>
              <FileName>drv_crypto.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\HAL_Drivers\drv_crypto.c</FilePath>
            </File>
            <File>
              <FileName>drv_wdt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\HAL_Drivers\drv_wdt.c</FilePath>
            </File>
            <File>
              <FileName>drv_usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\HAL_Drivers\drv_usart.c</FilePath>
            </File>
            <File>
              <FileName>drv_common.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\HAL_Drivers\drv_common.c</FilePath>
            </File>
            <File>
              <FileName>drv_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\HAL_Drivers\drv_rtc.c</FilePath>
            </File>
            <File>
              <FileName>cpuusage.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\examples\kernel\cpuusage.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Applications</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>applications\main.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>CPU</GroupName>
          <Files>
            <File>
              <FileName>div0.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\libcpu\arm\common\div0.c</FilePath>
            </File>
            <File>
              <FileName>backtrace.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\libcpu\arm\common\backtrace.c</FilePath>
            </File>
            <File>
              <FileName>showmem.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\libcpu\arm\common\showmem.c</FilePath>
            </File>
            <File>
              <FileName>cpuport.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\libcpu\arm\cortex-m4\cpuport.c</FilePath>
            </File>
            <File>
              <FileName>context_rvds.S</FileName>
              <FileType>2</FileType>
              <FilePath>..\..\..\libcpu\arm\cortex-m4\context_rvds.S</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>DeviceDrivers</GroupName>
          <GroupOption>
            <CommonProperty>
              <UseCPPCompiler>0</UseCPPCompiler>
              <RVCTCodeConst>0</RVCTCodeConst>
              <RVCTZI>0</RVCTZI>
              <RVCTOtherData>0</RVCTOtherData>
              <ModuleSelection>0</ModuleSelection>
              <IncludeInBuild>1</IncludeInBuild>
              <AlwaysBuild>0</AlwaysBuild>
              <GenerateAssemblyFile>0</GenerateAssemblyFile>
              <AssembleAssemblyFile>0</AssembleAssemblyFile>
              <PublicsOnly>0</PublicsOnly>
              <StopOnExitCode>3</StopOnExitCode>
              <CustomArgument></CustomArgument>
              <IncludeLibraryModules></IncludeLibraryModules>
              <ComprImg>0</ComprImg>
            </CommonProperty>
            <GroupArmAds>
              <Cads>
                <interw>2</interw>
                <Optim>0</Optim>
                <oTime>2</oTime>
                <SplitLS>2</SplitLS>
                <OneElfS>2</OneElfS>
                <Strict>2</Strict>
                <EnumInt>2</EnumInt>
                <PlainCh>2</PlainCh>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <wLevel>0</wLevel>
                <uThumb>2</uThumb>
                <uSurpInc>2</uSurpInc>
                <uC99>2</uC99>
                <uGnu>2</uGnu>
                <useXO>2</useXO>
                <v6Lang>0</v6Lang>
                <v6LangP>0</v6LangP>
                <vShortEn>2</vShortEn>
                <vShortWch>2</vShortWch>
                <v6Lto>2</v6Lto>
                <v6WtE>2</v6WtE>
                <v6Rtti>2</v6Rtti>
                <VariousControls>
                  <MiscControls> </MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Cads>
              <Aads>
                <interw>2</interw>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <thumb>2</thumb>
                <SplitLS>2</SplitLS>
                <SwStkChk>2</SwStkChk>
                <NoWarn>2</NoWarn>
                <uSurpInc>2</uSurpInc>
                <useXO>2</useXO>
                <ClangAsOpt>0</ClangAsOpt>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Aads>
            </GroupArmAds>
          </GroupOption>
          <Files>
            <File>
              <FileName>hwcrypto.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\hwcrypto\hwcrypto.c</FilePath>
            </File>
            <File>
              <FileName>pin.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\misc\pin.c</FilePath>
            </File>
            <File>
              <FileName>mtd_nor.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\mtd\mtd_nor.c</FilePath>
            </File>
            <File>
              <FileName>soft_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\rtc\soft_rtc.c</FilePath>
            </File>
            <File>
              <FileName>rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\rtc\rtc.c</FilePath>
            </File>
            <File>
              <FileName>serial.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\serial\serial.c</FilePath>
            </File>
            <File>
              <FileName>sfud.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\spi\sfud\src\sfud.c</FilePath>
            </File>
            <File>
              <FileName>spi_flash_sfud.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\spi\spi_flash_sfud.c</FilePath>
            </File>
            <File>
              <FileName>sfud_sfdp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\spi\sfud\src\sfud_sfdp.c</FilePath>
            </File>
            <File>
              <FileName>spi_core.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\spi\spi_core.c</FilePath>
            </File>
            <File>
              <FileName>spi_dev.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\spi\spi_dev.c</FilePath>
            </File>
            <File>
              <FileName>workqueue.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\src\workqueue.c</FilePath>
            </File>
            <File>
              <FileName>completion.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\src\completion.c</FilePath>
            </File>
            <File>
              <FileName>dataqueue.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\src\dataqueue.c</FilePath>
            </File>
            <File>
              <FileName>pipe.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\src\pipe.c</FilePath>
            </File>
            <File>
              <FileName>ringblk_buf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\src\ringblk_buf.c</FilePath>
            </File>
            <File>
              <FileName>waitqueue.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\src\waitqueue.c</FilePath>
            </File>
            <File>
              <FileName>ringbuffer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\src\ringbuffer.c</FilePath>
            </File>
            <File>
              <FileName>watchdog.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\watchdog\watchdog.c</FilePath>
            </File>
            <File>
              <FileName>sensor.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\sensors\sensor.c</FilePath>
            </File>
            <File>
              <FileName>sensor_cmd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\sensors\sensor_cmd.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>fal</GroupName>
          <GroupOption>
            <CommonProperty>
              <UseCPPCompiler>0</UseCPPCompiler>
              <RVCTCodeConst>0</RVCTCodeConst>
              <RVCTZI>0</RVCTZI>
              <RVCTOtherData>0</RVCTOtherData>
              <ModuleSelection>0</ModuleSelection>
              <IncludeInBuild>1</IncludeInBuild>
              <AlwaysBuild>0</AlwaysBuild>
              <GenerateAssemblyFile>0</GenerateAssemblyFile>
              <AssembleAssemblyFile>0</AssembleAssemblyFile>
              <PublicsOnly>0</PublicsOnly>
              <StopOnExitCode>3</StopOnExitCode>
              <CustomArgument></CustomArgument>
              <IncludeLibraryModules></IncludeLibraryModules>
              <ComprImg>0</ComprImg>
            </CommonProperty>
            <GroupArmAds>
              <Cads>
                <interw>2</interw>
                <Optim>0</Optim>
                <oTime>2</oTime>
                <SplitLS>2</SplitLS>
                <OneElfS>2</OneElfS>
                <Strict>2</Strict>
                <EnumInt>2</EnumInt>
                <PlainCh>2</PlainCh>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <wLevel>0</wLevel>
                <uThumb>2</uThumb>
                <uSurpInc>2</uSurpInc>
                <uC99>2</uC99>
                <uGnu>2</uGnu>
                <useXO>2</useXO>
                <v6Lang>0</v6Lang>
                <v6LangP>0</v6LangP>
                <vShortEn>2</vShortEn>
                <vShortWch>2</vShortWch>
                <v6Lto>2</v6Lto>
                <v6WtE>2</v6WtE>
                <v6Rtti>2</v6Rtti>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define> </Define>
                  <Undefine> </Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Cads>
              <Aads>
                <interw>2</interw>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <thumb>2</thumb>
                <SplitLS>2</SplitLS>
                <SwStkChk>2</SwStkChk>
                <NoWarn>2</NoWarn>
                <uSurpInc>2</uSurpInc>
                <useXO>2</useXO>
                <ClangAsOpt>0</ClangAsOpt>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Aads>
            </GroupArmAds>
          </GroupOption>
          <Files>
            <File>
              <FileName>fal_rtt.c</FileName>
              <FileType>1</FileType>
              <FilePath>packages\fal-v0.5.0\src\fal_rtt.c</FilePath>
            </File>
            <File>
              <FileName>fal.c</FileName>
              <FileType>1</FileType>
              <FilePath>packages\fal-v0.5.0\src\fal.c</FilePath>
            </File>
            <File>
              <FileName>fal_flash_sfud_port.c</FileName>
              <FileType>1</FileType>
              <FilePath>packages\fal-v0.5.0\samples\porting\fal_flash_sfud_port.c</FilePath>
            </File>
            <File>
              <FileName>fal_partition.c</FileName>
              <FileType>1</FileType>
              <FilePath>packages\fal-v0.5.0\src\fal_partition.c</FilePath>
            </File>
            <File>
              <FileName>fal_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>packages\fal-v0.5.0\src\fal_flash.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Filesystem</GroupName>
          <Files>
            <File>
              <FileName>dfs_posix.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\dfs\src\dfs_posix.c</FilePath>
            </File>
            <File>
              <FileName>dfs_fs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\dfs\src\dfs_fs.c</FilePath>
            </File>
            <File>
              <FileName>dfs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\dfs\src\dfs.c</FilePath>
            </File>
            <File>
              <FileName>dfs_file.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\dfs\src\dfs_file.c</FilePath>
            </File>
            <File>
              <FileName>devfs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\dfs\filesystems\devfs\devfs.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Finsh</GroupName>
          <Files>
            <File>
              <FileName>shell.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\finsh\shell.c</FilePath>
            </File>
            <File>
              <FileName>msh.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\finsh\msh.c</FilePath>
            </File>
            <File>
              <FileName>msh_file.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\finsh\msh_file.c</FilePath>
            </File>
            <File>
              <FileName>cmd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\finsh\cmd.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Kernel</GroupName>
          <Files>
            <File>
              <FileName>mem.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\mem.c</FilePath>
            </File>
            <File>
              <FileName>scheduler.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\scheduler.c</FilePath>
            </File>
            <File>
              <FileName>idle.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\idle.c</FilePath>
            </File>
            <File>
              <FileName>timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\timer.c</FilePath>
            </File>
            <File>
              <FileName>thread.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\thread.c</FilePath>
            </File>
            <File>
              <FileName>device.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\device.c</FilePath>
            </File>
            <File>
              <FileName>mempool.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\mempool.c</FilePath>
            </File>
            <File>
              <FileName>irq.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\irq.c</FilePath>
            </File>
            <File>
              <FileName>kservice.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\kservice.c</FilePath>
            </File>
            <File>
              <FileName>clock.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\clock.c</FilePath>
            </File>
            <File>
              <FileName>object.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\object.c</FilePath>
            </File>
            <File>
              <FileName>components.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\components.c</FilePath>
            </File>
            <File>
              <FileName>ipc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\ipc.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>libc</GroupName>
          <Files>
            <File>
              <FileName>time.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\libc\compilers\common\time.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Libraries</GroupName>
          <Files>
            <File>
              <FileName>stm32g4xx_hal_dma_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_dma_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_gpio.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_rcc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rcc_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_iwdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_iwdg.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_adc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_adc_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rtc.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_uart_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_uart_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_rtc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rtc_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_tim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_tim.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_cryp_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_cryp_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_pwr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_pwr.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_dac_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_dac_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_flash_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_flash_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_fdcan.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_fdcan.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_cryp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_cryp.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_usart_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_usart_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_dac.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_dac.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_rng.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rng.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_tim_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_tim_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_qspi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_qspi.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_wwdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_wwdg.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_usart.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_cortex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_cortex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_rcc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rcc.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_dma.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_uart.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_flash.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_pwr_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_pwr_ex.c</FilePath>
            </File>
            <File>
              <FileName>system_stm32g4xx.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\CMSIS\Device\ST\STM32G4xx\Source\Templates\system_stm32g4xx.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_spi.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_adc.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_nor.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_nor.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_crc.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_cordic.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_cordic.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_ll_cordic.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_ll_cordic.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_ll_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_ll_gpio.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>littlefs</GroupName>
          <Files>
            <File>
              <FileName>dfs_lfs.c</FileName>
              <FileType>1</FileType>
              <FilePath>packages\littlefs-latest\dfs_lfs.c</FilePath>
            </File>
            <File>
              <FileName>lfs_util.c</FileName>
              <FileType>1</FileType>
              <FilePath>packages\littlefs-latest\lfs_util.c</FilePath>
            </File>
            <File>
              <FileName>lfs.c</FileName>
              <FileType>1</FileType>
              <FilePath>packages\littlefs-latest\lfs.c</FilePath>
            </File>
            <File>
              <FileName>lfs_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>packages\littlefs-latest\lfs_crc.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>SAL</GroupName>
          <GroupOption>
            <CommonProperty>
              <UseCPPCompiler>0</UseCPPCompiler>
              <RVCTCodeConst>0</RVCTCodeConst>
              <RVCTZI>0</RVCTZI>
              <RVCTOtherData>0</RVCTOtherData>
              <ModuleSelection>0</ModuleSelection>
              <IncludeInBuild>0</IncludeInBuild>
              <AlwaysBuild>0</AlwaysBuild>
              <GenerateAssemblyFile>2</GenerateAssemblyFile>
              <AssembleAssemblyFile>2</AssembleAssemblyFile>
              <PublicsOnly>2</PublicsOnly>
              <StopOnExitCode>11</StopOnExitCode>
              <CustomArgument></CustomArgument>
              <IncludeLibraryModules></IncludeLibraryModules>
              <ComprImg>1</ComprImg>
            </CommonProperty>
            <GroupArmAds>
              <Cads>
                <interw>2</interw>
                <Optim>0</Optim>
                <oTime>2</oTime>
                <SplitLS>2</SplitLS>
                <OneElfS>2</OneElfS>
                <Strict>2</Strict>
                <EnumInt>2</EnumInt>
                <PlainCh>2</PlainCh>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <wLevel>0</wLevel>
                <uThumb>2</uThumb>
                <uSurpInc>2</uSurpInc>
                <uC99>2</uC99>
                <uGnu>2</uGnu>
                <useXO>2</useXO>
                <v6Lang>0</v6Lang>
                <v6LangP>0</v6LangP>
                <vShortEn>2</vShortEn>
                <vShortWch>2</vShortWch>
                <v6Lto>2</v6Lto>
                <v6WtE>2</v6WtE>
                <v6Rtti>2</v6Rtti>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Cads>
              <Aads>
                <interw>2</interw>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <thumb>2</thumb>
                <SplitLS>2</SplitLS>
                <SwStkChk>2</SwStkChk>
                <NoWarn>2</NoWarn>
                <uSurpInc>2</uSurpInc>
                <useXO>2</useXO>
                <ClangAsOpt>0</ClangAsOpt>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Aads>
            </GroupArmAds>
          </GroupOption>
          <Files>
            <File>
              <FileName>netdev_ipaddr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\net\netdev\src\netdev_ipaddr.c</FilePath>
            </File>
            <File>
              <FileName>netdev.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\net\netdev\src\netdev.c</FilePath>
            </File>
            <File>
              <FileName>sal_socket.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\net\sal_socket\src\sal_socket.c</FilePath>
            </File>
            <File>
              <FileName>net_netdb.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\net\sal_socket\socket\net_netdb.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Utilities</GroupName>
          <Files>
            <File>
              <FileName>ulog.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\utilities\ulog\ulog.c</FilePath>
            </File>
            <File>
              <FileName>console_be.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\utilities\ulog\backend\console_be.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>FlashDB</GroupName>
          <Files>
            <File>
              <FileName>fdb_cmd.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\FlashDB-v1.1.0\src\fdb_cmd.c</FilePath>
            </File>
            <File>
              <FileName>fdb.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\FlashDB-v1.1.0\src\fdb.c</FilePath>
            </File>
            <File>
              <FileName>fdb_kvdb.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\FlashDB-v1.1.0\src\fdb_kvdb.c</FilePath>
            </File>
            <File>
              <FileName>fdb_tsdb.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\FlashDB-v1.1.0\src\fdb_tsdb.c</FilePath>
            </File>
            <File>
              <FileName>fdb_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\FlashDB-v1.1.0\src\fdb_utils.c</FilePath>
            </File>
            <File>
              <FileName>kvdb_basic_sample.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\FlashDB-v1.1.0\samples\kvdb_basic_sample.c</FilePath>
            </File>
            <File>
              <FileName>tsdb_sample.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\FlashDB-v1.1.0\samples\tsdb_sample.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>lid3dh</GroupName>
          <Files>
            <File>
              <FileName>lis3dh_reg.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\lis3dh\lis3dh_reg.c</FilePath>
            </File>
            <File>
              <FileName>port_sensor.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\lis3dh\port_sensor.c</FilePath>
            </File>
            <File>
              <FileName>sensor_st_lis3dh.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\lis3dh\sensor_st_lis3dh.c</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
    <Target>
      <TargetName>例行试验</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>6160000::V6.16::ARMCLANG</pCCUsed>
      <uAC6>1</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>STM32G474RETx</Device>
          <Vendor>STMicroelectronics</Vendor>
          <PackID>Keil.STM32G4xx_DFP.1.2.0</PackID>
          <PackURL>http://www.keil.com/pack/</PackURL>
          <Cpu>IRAM(0x20000000,0x00020000) IROM(0x08000000,0x00080000) CPUTYPE("Cortex-M4") FPU2 CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN1 -FF0STM32G4xx_512_Dual -********** -FL080000 -FP0($$Device:STM32G474RETx$CMSIS\Flash\STM32G4xx_512_Dual.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:STM32G474RETx$Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:STM32G474RETx$CMSIS\SVD\STM32G474xx.svd</SFDFile>
          <bCustSvd>1</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>..\..\..\..\build\inv\</OutputDirectory>
          <OutputName>PMSM12_Test_A02_T</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>..\..\..\..\build\inv\list\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>fromelf --bin !L --output ./obj/@H.bin</UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> -REMAP -MPU</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> -MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3></Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>0</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x80000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x80000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>2</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>1</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>3</v6Lang>
            <v6LangP>5</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>HW_PVPB08,VFD_TEST_DEBUG,NOUSE_MCSDK_UART,ICS_SENSORS,USE_HAL_DRIVER, LFS_CONFIG=lfs_config.h, __RTTHREAD__, STM32G474xx, __CLK_TCK=RT_TICK_PER_SECOND,ARM_MATH_CM4</Define>
              <Undefine></Undefine>
              <IncludePath>applications;.;..\..\..\components\net\at\include;..\..\..\components\net\at\at_socket;packages\at_device-latest\inc;packages\at_device-latest\class\esp8266;..\..\..\libcpu\arm\common;..\..\..\libcpu\arm\cortex-m4;..\..\..\components\drivers\hwcrypto;.;..\..\..\components\drivers\include;..\..\..\components\drivers\include;..\..\..\components\drivers\include;..\..\..\components\drivers\include;..\..\..\components\drivers\spi;..\..\..\components\drivers\include;..\..\..\components\drivers\spi\sfud\inc;..\..\..\components\drivers\include;..\..\..\components\drivers\include;board;board\CubeMX_Config\Inc;..\libraries\HAL_Drivers;..\libraries\HAL_Drivers\config;packages\fal-v0.5.0\inc;..\..\..\components\dfs\include;..\..\..\components\dfs\filesystems\devfs;..\..\..\components\finsh;.;..\..\..\include;..\..\..\components\libc\compilers\common;..\..\..\components\libc\compilers\common\nogcc;..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Inc;..\libraries\STM32G4xx_HAL\CMSIS\Device\ST\STM32G4xx\Include;..\libraries\STM32G4xx_HAL\CMSIS\Include;packages\littlefs-latest;packages\netutils-v1.3.1\ntp;..\..\..\components\net\netdev\include;..\..\..\components\net\sal_socket\include;..\..\..\components\net\sal_socket\include\socket;..\..\..\components\net\sal_socket\impl;..\..\..\components\net\sal_socket\include\socket\sys_socket;..\..\..\examples\utest\testcases\kernel;..\..\..\components\utilities\ulog;.\packages\FlashDB-v1.1.0\inc;packages\freemodbus-latest\modbus\include;packages\freemodbus-latest\modbus\rtu;packages\freemodbus-latest\modbus\ascii;packages\freemodbus-latest\modbus\tcp;packages\freemodbus-latest\port;.\BsmProtocol;.\CRC;..\libraries\STM32G4xx_HAL\CMSIS\DSP\Include;.\user\inc;.\framework_foc\acim_svpwm;.\framework_pfc;.\framework_boost;.\bsmcomm_protocol;.\customer_port\merak;..\..\..\components\drivers\include\drivers;.\packages\lis3dh;.\framework_foc\pmsm_inovance\Core\Inc;.\framework_foc\pmsm_inovance\Core\Inc\INC_USER;.\framework_foc\pmsm_inovance\Core\Src\userApps;.\framework_foc\pmsm_inovance</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>4</ClangAsOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>0</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x08000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile>.\board\linker_scripts\link.sct</ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>customer/yingsai</GroupName>
          <Files>
            <File>
              <FileName>customer_port.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\customer_port\merak\customer_port.h</FilePath>
            </File>
            <File>
              <FileName>app_can_port.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\customer_port\merak\app_can_port.c</FilePath>
            </File>
            <File>
              <FileName>app_rs485_port.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\customer_port\merak\app_rs485_port.c</FilePath>
            </File>
            <File>
              <FileName>customer_port.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\customer_port\merak\customer_port.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>user</GroupName>
          <GroupOption>
            <CommonProperty>
              <UseCPPCompiler>0</UseCPPCompiler>
              <RVCTCodeConst>0</RVCTCodeConst>
              <RVCTZI>0</RVCTZI>
              <RVCTOtherData>0</RVCTOtherData>
              <ModuleSelection>0</ModuleSelection>
              <IncludeInBuild>1</IncludeInBuild>
              <AlwaysBuild>0</AlwaysBuild>
              <GenerateAssemblyFile>0</GenerateAssemblyFile>
              <AssembleAssemblyFile>0</AssembleAssemblyFile>
              <PublicsOnly>0</PublicsOnly>
              <StopOnExitCode>3</StopOnExitCode>
              <CustomArgument></CustomArgument>
              <IncludeLibraryModules></IncludeLibraryModules>
              <ComprImg>0</ComprImg>
            </CommonProperty>
            <GroupArmAds>
              <Cads>
                <interw>2</interw>
                <Optim>0</Optim>
                <oTime>2</oTime>
                <SplitLS>2</SplitLS>
                <OneElfS>2</OneElfS>
                <Strict>2</Strict>
                <EnumInt>2</EnumInt>
                <PlainCh>2</PlainCh>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <wLevel>0</wLevel>
                <uThumb>2</uThumb>
                <uSurpInc>2</uSurpInc>
                <uC99>2</uC99>
                <uGnu>2</uGnu>
                <useXO>2</useXO>
                <v6Lang>0</v6Lang>
                <v6LangP>0</v6LangP>
                <vShortEn>2</vShortEn>
                <vShortWch>2</vShortWch>
                <v6Lto>2</v6Lto>
                <v6WtE>2</v6WtE>
                <v6Rtti>2</v6Rtti>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define> </Define>
                  <Undefine> </Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Cads>
              <Aads>
                <interw>2</interw>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <thumb>2</thumb>
                <SplitLS>2</SplitLS>
                <SwStkChk>2</SwStkChk>
                <NoWarn>2</NoWarn>
                <uSurpInc>2</uSurpInc>
                <useXO>2</useXO>
                <ClangAsOpt>0</ClangAsOpt>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Aads>
            </GroupArmAds>
          </GroupOption>
          <Files>
            <File>
              <FileName>uapp.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\user\inc\uapp.h</FilePath>
            </File>
            <File>
              <FileName>app_user.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_user.c</FilePath>
            </File>
            <File>
              <FileName>app_sub.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_sub.c</FilePath>
            </File>
            <File>
              <FileName>app_main.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_main.c</FilePath>
            </File>
            <File>
              <FileName>alg_diag.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\alg_diag.c</FilePath>
            </File>
            <File>
              <FileName>app_diag.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_diag.c</FilePath>
            </File>
            <File>
              <FileName>drv_io.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\drv_io.c</FilePath>
            </File>
            <File>
              <FileName>drv_hal_main.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\drv_hal_main.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_msp.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\stm32g4xx_hal_msp.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_it.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\stm32g4xx_it.c</FilePath>
            </File>
            <File>
              <FileName>alg_dio_edge.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\alg_dio_edge.c</FilePath>
            </File>
            <File>
              <FileName>alg_pcmaster.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\alg_pcmaster.c</FilePath>
            </File>
            <File>
              <FileName>alg_toolbox.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\alg_toolbox.c</FilePath>
            </File>
            <File>
              <FileName>alg_uapp.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\alg_uapp.c</FilePath>
            </File>
            <File>
              <FileName>app_cmd.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_cmd.c</FilePath>
            </File>
            <File>
              <FileName>app_pcmaster.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_pcmaster.c</FilePath>
            </File>
            <File>
              <FileName>app_record.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_record.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>2</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>1</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <uGnu>2</uGnu>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>2</vShortEn>
                    <vShortWch>2</vShortWch>
                    <v6Lto>2</v6Lto>
                    <v6WtE>2</v6WtE>
                    <v6Rtti>2</v6Rtti>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>app_test.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_test.c</FilePath>
            </File>
            <File>
              <FileName>drv_fal_flash_stm32g4_port.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\drv_fal_flash_stm32g4_port.c</FilePath>
            </File>
            <File>
              <FileName>drv_rs485.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\drv_rs485.c</FilePath>
            </File>
            <File>
              <FileName>drv_fdcan.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\drv_fdcan.c</FilePath>
            </File>
            <File>
              <FileName>alg_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\alg_crc.c</FilePath>
            </File>
            <File>
              <FileName>alg_modbus_common.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\alg_modbus_common.c</FilePath>
            </File>
            <File>
              <FileName>app_ad.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_ad.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>2</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>1</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <uGnu>2</uGnu>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>2</vShortEn>
                    <vShortWch>2</vShortWch>
                    <v6Lto>2</v6Lto>
                    <v6WtE>2</v6WtE>
                    <v6Rtti>2</v6Rtti>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>alg_ntc_convert.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\alg_ntc_convert.c</FilePath>
            </File>
            <File>
              <FileName>app_alternate_comm.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_alternate_comm.c</FilePath>
            </File>
            <File>
              <FileName>drv_soft_iic_hdc1080.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\drv_soft_iic_hdc1080.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>2</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>1</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <uGnu>2</uGnu>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>2</vShortEn>
                    <vShortWch>2</vShortWch>
                    <v6Lto>2</v6Lto>
                    <v6WtE>2</v6WtE>
                    <v6Rtti>2</v6Rtti>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>app_dpll3p.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_dpll3p.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>foc/inovance</GroupName>
          <Files>
            <File>
              <FileName>user_mcsdk_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\user_mcsdk_task.c</FilePath>
            </File>
            <File>
              <FileName>arm_abs_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_abs_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_abs_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_abs_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_common_tables.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_common_tables.c</FilePath>
            </File>
            <File>
              <FileName>arm_cos_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_cos_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cos_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_cos_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cos_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_cos_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_float_to_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_float_to_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_q15_to_float.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_q15_to_float.c</FilePath>
            </File>
            <File>
              <FileName>arm_q15_to_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_q15_to_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_q31_to_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_q31_to_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_cos_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_sin_cos_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_cos_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_sin_cos_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_sin_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_sin_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_sin_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_sqrt_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_sqrt_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_sqrt_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_sqrt_q31.c</FilePath>
            </File>
            <File>
              <FileName>DeviceInit.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\DeviceInit.c</FilePath>
            </File>
            <File>
              <FileName>f_comm.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_comm.c</FilePath>
            </File>
            <File>
              <FileName>f_error.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_error.c</FilePath>
            </File>
            <File>
              <FileName>f_frqSrc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_frqSrc.c</FilePath>
            </File>
            <File>
              <FileName>f_funcCode.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_funcCode.c</FilePath>
            </File>
            <File>
              <FileName>f_interface.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_interface.c</FilePath>
            </File>
            <File>
              <FileName>f_main.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_main.c</FilePath>
            </File>
            <File>
              <FileName>f_runSrc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_runSrc.c</FilePath>
            </File>
            <File>
              <FileName>f_runSrc_accDecFrq.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_runSrc_accDecFrq.c</FilePath>
            </File>
            <File>
              <FileName>MotorCarrier.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorCarrier.c</FilePath>
            </File>
            <File>
              <FileName>MotorConst.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorConst.c</FilePath>
            </File>
            <File>
              <FileName>MotorCurrentTransform.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorCurrentTransform.c</FilePath>
            </File>
            <File>
              <FileName>MotorDataExchange.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorDataExchange.c</FilePath>
            </File>
            <File>
              <FileName>MotorEncoder.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorEncoder.c</FilePath>
            </File>
            <File>
              <FileName>MotorInfoCollect.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorInfoCollect.c</FilePath>
            </File>
            <File>
              <FileName>MotorInvProtect.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorInvProtect.c</FilePath>
            </File>
            <File>
              <FileName>MotorIPMSVC.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorIPMSVC.c</FilePath>
            </File>
            <File>
              <FileName>MotorMain.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorMain.c</FilePath>
            </File>
            <File>
              <FileName>MotorParChange.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorParChange.c</FilePath>
            </File>
            <File>
              <FileName>MotorParEst.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorParEst.c</FilePath>
            </File>
            <File>
              <FileName>MotorPmsmMain.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorPmsmMain.c</FilePath>
            </File>
            <File>
              <FileName>MotorPmsmParEst.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorPmsmParEst.c</FilePath>
            </File>
            <File>
              <FileName>MotorPublicCal.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorPublicCal.c</FilePath>
            </File>
            <File>
              <FileName>MotorPWM.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorPWM.c</FilePath>
            </File>
            <File>
              <FileName>MotorSpeed.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorSpeed.c</FilePath>
            </File>
            <File>
              <FileName>MotorVar.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorVar.c</FilePath>
            </File>
            <File>
              <FileName>MotorVCMain.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorVCMain.c</FilePath>
            </File>
            <File>
              <FileName>MotorVF.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorVF.c</FilePath>
            </File>
            <File>
              <FileName>Scope.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\Scope.c</FilePath>
            </File>
            <File>
              <FileName>SubPrg.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\SubPrg.c</FilePath>
            </File>
            <File>
              <FileName>Svpwm.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\Svpwm.c</FilePath>
            </File>
            <File>
              <FileName>testApp.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\testApp.c</FilePath>
            </File>
            <File>
              <FileName>MotorFlyingStart.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorFlyingStart.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>protocol_comm</GroupName>
          <Files>
            <File>
              <FileName>app_bsm_com_outernet.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsmcomm_protocol\app_bsm_com_outernet.c</FilePath>
            </File>
            <File>
              <FileName>api_bsm_com_protocol.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsmcomm_protocol\api_bsm_com_protocol.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Drivers</GroupName>
          <Files>
            <File>
              <FileName>rtconfig.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\rtconfig.h</FilePath>
            </File>
            <File>
              <FileName>startup_stm32g474xx.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\CMSIS\Device\ST\STM32G4xx\Source\Templates\arm\startup_stm32g474xx.s</FilePath>
            </File>
            <File>
              <FileName>board.c</FileName>
              <FileType>1</FileType>
              <FilePath>board\board.c</FilePath>
            </File>
            <File>
              <FileName>drv_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\HAL_Drivers\drv_gpio.c</FilePath>
            </File>
            <File>
              <FileName>drv_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\HAL_Drivers\drv_spi.c</FilePath>
            </File>
            <File>
              <FileName>drv_crypto.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\HAL_Drivers\drv_crypto.c</FilePath>
            </File>
            <File>
              <FileName>drv_wdt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\HAL_Drivers\drv_wdt.c</FilePath>
            </File>
            <File>
              <FileName>drv_usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\HAL_Drivers\drv_usart.c</FilePath>
            </File>
            <File>
              <FileName>drv_common.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\HAL_Drivers\drv_common.c</FilePath>
            </File>
            <File>
              <FileName>drv_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\HAL_Drivers\drv_rtc.c</FilePath>
            </File>
            <File>
              <FileName>cpuusage.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\examples\kernel\cpuusage.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Applications</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>applications\main.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>CPU</GroupName>
          <Files>
            <File>
              <FileName>div0.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\libcpu\arm\common\div0.c</FilePath>
            </File>
            <File>
              <FileName>backtrace.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\libcpu\arm\common\backtrace.c</FilePath>
            </File>
            <File>
              <FileName>showmem.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\libcpu\arm\common\showmem.c</FilePath>
            </File>
            <File>
              <FileName>cpuport.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\libcpu\arm\cortex-m4\cpuport.c</FilePath>
            </File>
            <File>
              <FileName>context_rvds.S</FileName>
              <FileType>2</FileType>
              <FilePath>..\..\..\libcpu\arm\cortex-m4\context_rvds.S</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>DeviceDrivers</GroupName>
          <GroupOption>
            <CommonProperty>
              <UseCPPCompiler>0</UseCPPCompiler>
              <RVCTCodeConst>0</RVCTCodeConst>
              <RVCTZI>0</RVCTZI>
              <RVCTOtherData>0</RVCTOtherData>
              <ModuleSelection>0</ModuleSelection>
              <IncludeInBuild>1</IncludeInBuild>
              <AlwaysBuild>0</AlwaysBuild>
              <GenerateAssemblyFile>0</GenerateAssemblyFile>
              <AssembleAssemblyFile>0</AssembleAssemblyFile>
              <PublicsOnly>0</PublicsOnly>
              <StopOnExitCode>3</StopOnExitCode>
              <CustomArgument></CustomArgument>
              <IncludeLibraryModules></IncludeLibraryModules>
              <ComprImg>0</ComprImg>
            </CommonProperty>
            <GroupArmAds>
              <Cads>
                <interw>2</interw>
                <Optim>0</Optim>
                <oTime>2</oTime>
                <SplitLS>2</SplitLS>
                <OneElfS>2</OneElfS>
                <Strict>2</Strict>
                <EnumInt>2</EnumInt>
                <PlainCh>2</PlainCh>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <wLevel>0</wLevel>
                <uThumb>2</uThumb>
                <uSurpInc>2</uSurpInc>
                <uC99>2</uC99>
                <uGnu>2</uGnu>
                <useXO>2</useXO>
                <v6Lang>0</v6Lang>
                <v6LangP>0</v6LangP>
                <vShortEn>2</vShortEn>
                <vShortWch>2</vShortWch>
                <v6Lto>2</v6Lto>
                <v6WtE>2</v6WtE>
                <v6Rtti>2</v6Rtti>
                <VariousControls>
                  <MiscControls> </MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Cads>
              <Aads>
                <interw>2</interw>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <thumb>2</thumb>
                <SplitLS>2</SplitLS>
                <SwStkChk>2</SwStkChk>
                <NoWarn>2</NoWarn>
                <uSurpInc>2</uSurpInc>
                <useXO>2</useXO>
                <ClangAsOpt>0</ClangAsOpt>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Aads>
            </GroupArmAds>
          </GroupOption>
          <Files>
            <File>
              <FileName>hwcrypto.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\hwcrypto\hwcrypto.c</FilePath>
            </File>
            <File>
              <FileName>pin.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\misc\pin.c</FilePath>
            </File>
            <File>
              <FileName>mtd_nor.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\mtd\mtd_nor.c</FilePath>
            </File>
            <File>
              <FileName>soft_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\rtc\soft_rtc.c</FilePath>
            </File>
            <File>
              <FileName>rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\rtc\rtc.c</FilePath>
            </File>
            <File>
              <FileName>serial.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\serial\serial.c</FilePath>
            </File>
            <File>
              <FileName>sfud.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\spi\sfud\src\sfud.c</FilePath>
            </File>
            <File>
              <FileName>spi_flash_sfud.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\spi\spi_flash_sfud.c</FilePath>
            </File>
            <File>
              <FileName>sfud_sfdp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\spi\sfud\src\sfud_sfdp.c</FilePath>
            </File>
            <File>
              <FileName>spi_core.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\spi\spi_core.c</FilePath>
            </File>
            <File>
              <FileName>spi_dev.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\spi\spi_dev.c</FilePath>
            </File>
            <File>
              <FileName>workqueue.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\src\workqueue.c</FilePath>
            </File>
            <File>
              <FileName>completion.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\src\completion.c</FilePath>
            </File>
            <File>
              <FileName>dataqueue.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\src\dataqueue.c</FilePath>
            </File>
            <File>
              <FileName>pipe.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\src\pipe.c</FilePath>
            </File>
            <File>
              <FileName>ringblk_buf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\src\ringblk_buf.c</FilePath>
            </File>
            <File>
              <FileName>waitqueue.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\src\waitqueue.c</FilePath>
            </File>
            <File>
              <FileName>ringbuffer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\src\ringbuffer.c</FilePath>
            </File>
            <File>
              <FileName>watchdog.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\watchdog\watchdog.c</FilePath>
            </File>
            <File>
              <FileName>sensor.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\sensors\sensor.c</FilePath>
            </File>
            <File>
              <FileName>sensor_cmd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\sensors\sensor_cmd.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>fal</GroupName>
          <GroupOption>
            <CommonProperty>
              <UseCPPCompiler>0</UseCPPCompiler>
              <RVCTCodeConst>0</RVCTCodeConst>
              <RVCTZI>0</RVCTZI>
              <RVCTOtherData>0</RVCTOtherData>
              <ModuleSelection>0</ModuleSelection>
              <IncludeInBuild>1</IncludeInBuild>
              <AlwaysBuild>0</AlwaysBuild>
              <GenerateAssemblyFile>0</GenerateAssemblyFile>
              <AssembleAssemblyFile>0</AssembleAssemblyFile>
              <PublicsOnly>0</PublicsOnly>
              <StopOnExitCode>3</StopOnExitCode>
              <CustomArgument></CustomArgument>
              <IncludeLibraryModules></IncludeLibraryModules>
              <ComprImg>0</ComprImg>
            </CommonProperty>
            <GroupArmAds>
              <Cads>
                <interw>2</interw>
                <Optim>0</Optim>
                <oTime>2</oTime>
                <SplitLS>2</SplitLS>
                <OneElfS>2</OneElfS>
                <Strict>2</Strict>
                <EnumInt>2</EnumInt>
                <PlainCh>2</PlainCh>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <wLevel>0</wLevel>
                <uThumb>2</uThumb>
                <uSurpInc>2</uSurpInc>
                <uC99>2</uC99>
                <uGnu>2</uGnu>
                <useXO>2</useXO>
                <v6Lang>0</v6Lang>
                <v6LangP>0</v6LangP>
                <vShortEn>2</vShortEn>
                <vShortWch>2</vShortWch>
                <v6Lto>2</v6Lto>
                <v6WtE>2</v6WtE>
                <v6Rtti>2</v6Rtti>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define> </Define>
                  <Undefine> </Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Cads>
              <Aads>
                <interw>2</interw>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <thumb>2</thumb>
                <SplitLS>2</SplitLS>
                <SwStkChk>2</SwStkChk>
                <NoWarn>2</NoWarn>
                <uSurpInc>2</uSurpInc>
                <useXO>2</useXO>
                <ClangAsOpt>0</ClangAsOpt>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Aads>
            </GroupArmAds>
          </GroupOption>
          <Files>
            <File>
              <FileName>fal_rtt.c</FileName>
              <FileType>1</FileType>
              <FilePath>packages\fal-v0.5.0\src\fal_rtt.c</FilePath>
            </File>
            <File>
              <FileName>fal.c</FileName>
              <FileType>1</FileType>
              <FilePath>packages\fal-v0.5.0\src\fal.c</FilePath>
            </File>
            <File>
              <FileName>fal_flash_sfud_port.c</FileName>
              <FileType>1</FileType>
              <FilePath>packages\fal-v0.5.0\samples\porting\fal_flash_sfud_port.c</FilePath>
            </File>
            <File>
              <FileName>fal_partition.c</FileName>
              <FileType>1</FileType>
              <FilePath>packages\fal-v0.5.0\src\fal_partition.c</FilePath>
            </File>
            <File>
              <FileName>fal_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>packages\fal-v0.5.0\src\fal_flash.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Filesystem</GroupName>
          <Files>
            <File>
              <FileName>dfs_posix.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\dfs\src\dfs_posix.c</FilePath>
            </File>
            <File>
              <FileName>dfs_fs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\dfs\src\dfs_fs.c</FilePath>
            </File>
            <File>
              <FileName>dfs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\dfs\src\dfs.c</FilePath>
            </File>
            <File>
              <FileName>dfs_file.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\dfs\src\dfs_file.c</FilePath>
            </File>
            <File>
              <FileName>devfs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\dfs\filesystems\devfs\devfs.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Finsh</GroupName>
          <Files>
            <File>
              <FileName>shell.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\finsh\shell.c</FilePath>
            </File>
            <File>
              <FileName>msh.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\finsh\msh.c</FilePath>
            </File>
            <File>
              <FileName>msh_file.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\finsh\msh_file.c</FilePath>
            </File>
            <File>
              <FileName>cmd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\finsh\cmd.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Kernel</GroupName>
          <Files>
            <File>
              <FileName>mem.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\mem.c</FilePath>
            </File>
            <File>
              <FileName>scheduler.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\scheduler.c</FilePath>
            </File>
            <File>
              <FileName>idle.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\idle.c</FilePath>
            </File>
            <File>
              <FileName>timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\timer.c</FilePath>
            </File>
            <File>
              <FileName>thread.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\thread.c</FilePath>
            </File>
            <File>
              <FileName>device.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\device.c</FilePath>
            </File>
            <File>
              <FileName>mempool.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\mempool.c</FilePath>
            </File>
            <File>
              <FileName>irq.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\irq.c</FilePath>
            </File>
            <File>
              <FileName>kservice.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\kservice.c</FilePath>
            </File>
            <File>
              <FileName>clock.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\clock.c</FilePath>
            </File>
            <File>
              <FileName>object.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\object.c</FilePath>
            </File>
            <File>
              <FileName>components.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\components.c</FilePath>
            </File>
            <File>
              <FileName>ipc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\ipc.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>libc</GroupName>
          <Files>
            <File>
              <FileName>time.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\libc\compilers\common\time.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Libraries</GroupName>
          <Files>
            <File>
              <FileName>stm32g4xx_hal_dma_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_dma_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_gpio.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_rcc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rcc_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_iwdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_iwdg.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_adc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_adc_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rtc.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_uart_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_uart_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_rtc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rtc_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_tim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_tim.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_cryp_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_cryp_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_pwr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_pwr.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_dac_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_dac_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_flash_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_flash_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_fdcan.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_fdcan.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_cryp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_cryp.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_usart_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_usart_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_dac.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_dac.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_rng.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rng.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_tim_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_tim_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_qspi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_qspi.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_wwdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_wwdg.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_usart.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_cortex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_cortex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_rcc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rcc.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_dma.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_uart.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_flash.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_pwr_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_pwr_ex.c</FilePath>
            </File>
            <File>
              <FileName>system_stm32g4xx.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\CMSIS\Device\ST\STM32G4xx\Source\Templates\system_stm32g4xx.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_spi.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_adc.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_nor.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_nor.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_crc.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_cordic.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_cordic.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_ll_cordic.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_ll_cordic.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_ll_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_ll_gpio.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>littlefs</GroupName>
          <Files>
            <File>
              <FileName>dfs_lfs.c</FileName>
              <FileType>1</FileType>
              <FilePath>packages\littlefs-latest\dfs_lfs.c</FilePath>
            </File>
            <File>
              <FileName>lfs_util.c</FileName>
              <FileType>1</FileType>
              <FilePath>packages\littlefs-latest\lfs_util.c</FilePath>
            </File>
            <File>
              <FileName>lfs.c</FileName>
              <FileType>1</FileType>
              <FilePath>packages\littlefs-latest\lfs.c</FilePath>
            </File>
            <File>
              <FileName>lfs_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>packages\littlefs-latest\lfs_crc.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>SAL</GroupName>
          <GroupOption>
            <CommonProperty>
              <UseCPPCompiler>0</UseCPPCompiler>
              <RVCTCodeConst>0</RVCTCodeConst>
              <RVCTZI>0</RVCTZI>
              <RVCTOtherData>0</RVCTOtherData>
              <ModuleSelection>0</ModuleSelection>
              <IncludeInBuild>0</IncludeInBuild>
              <AlwaysBuild>0</AlwaysBuild>
              <GenerateAssemblyFile>2</GenerateAssemblyFile>
              <AssembleAssemblyFile>2</AssembleAssemblyFile>
              <PublicsOnly>2</PublicsOnly>
              <StopOnExitCode>11</StopOnExitCode>
              <CustomArgument></CustomArgument>
              <IncludeLibraryModules></IncludeLibraryModules>
              <ComprImg>1</ComprImg>
            </CommonProperty>
            <GroupArmAds>
              <Cads>
                <interw>2</interw>
                <Optim>0</Optim>
                <oTime>2</oTime>
                <SplitLS>2</SplitLS>
                <OneElfS>2</OneElfS>
                <Strict>2</Strict>
                <EnumInt>2</EnumInt>
                <PlainCh>2</PlainCh>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <wLevel>0</wLevel>
                <uThumb>2</uThumb>
                <uSurpInc>2</uSurpInc>
                <uC99>2</uC99>
                <uGnu>2</uGnu>
                <useXO>2</useXO>
                <v6Lang>0</v6Lang>
                <v6LangP>0</v6LangP>
                <vShortEn>2</vShortEn>
                <vShortWch>2</vShortWch>
                <v6Lto>2</v6Lto>
                <v6WtE>2</v6WtE>
                <v6Rtti>2</v6Rtti>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Cads>
              <Aads>
                <interw>2</interw>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <thumb>2</thumb>
                <SplitLS>2</SplitLS>
                <SwStkChk>2</SwStkChk>
                <NoWarn>2</NoWarn>
                <uSurpInc>2</uSurpInc>
                <useXO>2</useXO>
                <ClangAsOpt>0</ClangAsOpt>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Aads>
            </GroupArmAds>
          </GroupOption>
          <Files>
            <File>
              <FileName>netdev_ipaddr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\net\netdev\src\netdev_ipaddr.c</FilePath>
            </File>
            <File>
              <FileName>netdev.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\net\netdev\src\netdev.c</FilePath>
            </File>
            <File>
              <FileName>sal_socket.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\net\sal_socket\src\sal_socket.c</FilePath>
            </File>
            <File>
              <FileName>net_netdb.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\net\sal_socket\socket\net_netdb.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Utilities</GroupName>
          <Files>
            <File>
              <FileName>ulog.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\utilities\ulog\ulog.c</FilePath>
            </File>
            <File>
              <FileName>console_be.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\utilities\ulog\backend\console_be.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>FlashDB</GroupName>
          <Files>
            <File>
              <FileName>fdb_cmd.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\FlashDB-v1.1.0\src\fdb_cmd.c</FilePath>
            </File>
            <File>
              <FileName>fdb.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\FlashDB-v1.1.0\src\fdb.c</FilePath>
            </File>
            <File>
              <FileName>fdb_kvdb.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\FlashDB-v1.1.0\src\fdb_kvdb.c</FilePath>
            </File>
            <File>
              <FileName>fdb_tsdb.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\FlashDB-v1.1.0\src\fdb_tsdb.c</FilePath>
            </File>
            <File>
              <FileName>fdb_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\FlashDB-v1.1.0\src\fdb_utils.c</FilePath>
            </File>
            <File>
              <FileName>kvdb_basic_sample.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\FlashDB-v1.1.0\samples\kvdb_basic_sample.c</FilePath>
            </File>
            <File>
              <FileName>tsdb_sample.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\FlashDB-v1.1.0\samples\tsdb_sample.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>lid3dh</GroupName>
          <Files>
            <File>
              <FileName>lis3dh_reg.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\lis3dh\lis3dh_reg.c</FilePath>
            </File>
            <File>
              <FileName>port_sensor.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\lis3dh\port_sensor.c</FilePath>
            </File>
            <File>
              <FileName>sensor_st_lis3dh.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\lis3dh\sensor_st_lis3dh.c</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
    <Target>
      <TargetName>单板测试</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>6160000::V6.16::ARMCLANG</pCCUsed>
      <uAC6>1</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>STM32G474RETx</Device>
          <Vendor>STMicroelectronics</Vendor>
          <PackID>Keil.STM32G4xx_DFP.1.2.0</PackID>
          <PackURL>http://www.keil.com/pack/</PackURL>
          <Cpu>IRAM(0x20000000,0x00020000) IROM(0x08000000,0x00080000) CPUTYPE("Cortex-M4") FPU2 CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN1 -FF0STM32G4xx_512_Dual -********** -FL080000 -FP0($$Device:STM32G474RETx$CMSIS\Flash\STM32G4xx_512_Dual.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:STM32G474RETx$Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:STM32G474RETx$CMSIS\SVD\STM32G474xx.svd</SFDFile>
          <bCustSvd>1</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>..\..\..\..\build\inv\</OutputDirectory>
          <OutputName>PMSM12_Test_A01_T</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>..\..\..\..\build\inv\list\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>fromelf --bin !L --output ./obj/@H.bin</UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> -REMAP -MPU</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> -MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3></Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>0</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x80000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x80000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>2</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>1</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>3</v6Lang>
            <v6LangP>5</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>HW_PVPB08,NOVFD_TEST_DEBUG,NOUSE_MCSDK_UART,ICS_SENSORS,USE_HAL_DRIVER, LFS_CONFIG=lfs_config.h, __RTTHREAD__, STM32G474xx, __CLK_TCK=RT_TICK_PER_SECOND,ARM_MATH_CM4</Define>
              <Undefine></Undefine>
              <IncludePath>applications;.;..\..\..\components\net\at\include;..\..\..\components\net\at\at_socket;packages\at_device-latest\inc;packages\at_device-latest\class\esp8266;..\..\..\libcpu\arm\common;..\..\..\libcpu\arm\cortex-m4;..\..\..\components\drivers\hwcrypto;.;..\..\..\components\drivers\include;..\..\..\components\drivers\include;..\..\..\components\drivers\include;..\..\..\components\drivers\include;..\..\..\components\drivers\spi;..\..\..\components\drivers\include;..\..\..\components\drivers\spi\sfud\inc;..\..\..\components\drivers\include;..\..\..\components\drivers\include;board;board\CubeMX_Config\Inc;..\libraries\HAL_Drivers;..\libraries\HAL_Drivers\config;packages\fal-v0.5.0\inc;..\..\..\components\dfs\include;..\..\..\components\dfs\filesystems\devfs;..\..\..\components\finsh;.;..\..\..\include;..\..\..\components\libc\compilers\common;..\..\..\components\libc\compilers\common\nogcc;..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Inc;..\libraries\STM32G4xx_HAL\CMSIS\Device\ST\STM32G4xx\Include;..\libraries\STM32G4xx_HAL\CMSIS\Include;packages\littlefs-latest;packages\netutils-v1.3.1\ntp;..\..\..\components\net\netdev\include;..\..\..\components\net\sal_socket\include;..\..\..\components\net\sal_socket\include\socket;..\..\..\components\net\sal_socket\impl;..\..\..\components\net\sal_socket\include\socket\sys_socket;..\..\..\examples\utest\testcases\kernel;..\..\..\components\utilities\ulog;.\packages\FlashDB-v1.1.0\inc;packages\freemodbus-latest\modbus\include;packages\freemodbus-latest\modbus\rtu;packages\freemodbus-latest\modbus\ascii;packages\freemodbus-latest\modbus\tcp;packages\freemodbus-latest\port;.\BsmProtocol;.\CRC;..\libraries\STM32G4xx_HAL\CMSIS\DSP\Include;.\user\inc;.\framework_foc\acim_svpwm;.\framework_pfc;.\framework_boost;.\bsmcomm_protocol;.\customer_port\merak;..\..\..\components\drivers\include\drivers;.\packages\lis3dh;.\framework_foc\pmsm_inovance\Core\Inc;.\framework_foc\pmsm_inovance\Core\Inc\INC_USER;.\framework_foc\pmsm_inovance\Core\Src\userApps;.\framework_foc\pmsm_inovance</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>4</ClangAsOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>0</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x08000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile>.\board\linker_scripts\link.sct</ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>customer/yingsai</GroupName>
          <Files>
            <File>
              <FileName>customer_port.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\customer_port\merak\customer_port.h</FilePath>
            </File>
            <File>
              <FileName>app_can_port.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\customer_port\merak\app_can_port.c</FilePath>
            </File>
            <File>
              <FileName>app_rs485_port.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\customer_port\merak\app_rs485_port.c</FilePath>
            </File>
            <File>
              <FileName>customer_port.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\customer_port\merak\customer_port.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>user</GroupName>
          <GroupOption>
            <CommonProperty>
              <UseCPPCompiler>0</UseCPPCompiler>
              <RVCTCodeConst>0</RVCTCodeConst>
              <RVCTZI>0</RVCTZI>
              <RVCTOtherData>0</RVCTOtherData>
              <ModuleSelection>0</ModuleSelection>
              <IncludeInBuild>1</IncludeInBuild>
              <AlwaysBuild>0</AlwaysBuild>
              <GenerateAssemblyFile>0</GenerateAssemblyFile>
              <AssembleAssemblyFile>0</AssembleAssemblyFile>
              <PublicsOnly>0</PublicsOnly>
              <StopOnExitCode>3</StopOnExitCode>
              <CustomArgument></CustomArgument>
              <IncludeLibraryModules></IncludeLibraryModules>
              <ComprImg>0</ComprImg>
            </CommonProperty>
            <GroupArmAds>
              <Cads>
                <interw>2</interw>
                <Optim>0</Optim>
                <oTime>2</oTime>
                <SplitLS>2</SplitLS>
                <OneElfS>2</OneElfS>
                <Strict>2</Strict>
                <EnumInt>2</EnumInt>
                <PlainCh>2</PlainCh>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <wLevel>0</wLevel>
                <uThumb>2</uThumb>
                <uSurpInc>2</uSurpInc>
                <uC99>2</uC99>
                <uGnu>2</uGnu>
                <useXO>2</useXO>
                <v6Lang>0</v6Lang>
                <v6LangP>0</v6LangP>
                <vShortEn>2</vShortEn>
                <vShortWch>2</vShortWch>
                <v6Lto>2</v6Lto>
                <v6WtE>2</v6WtE>
                <v6Rtti>2</v6Rtti>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define> </Define>
                  <Undefine> </Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Cads>
              <Aads>
                <interw>2</interw>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <thumb>2</thumb>
                <SplitLS>2</SplitLS>
                <SwStkChk>2</SwStkChk>
                <NoWarn>2</NoWarn>
                <uSurpInc>2</uSurpInc>
                <useXO>2</useXO>
                <ClangAsOpt>0</ClangAsOpt>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Aads>
            </GroupArmAds>
          </GroupOption>
          <Files>
            <File>
              <FileName>uapp.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\user\inc\uapp.h</FilePath>
            </File>
            <File>
              <FileName>app_user.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_user.c</FilePath>
            </File>
            <File>
              <FileName>app_sub.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_sub.c</FilePath>
            </File>
            <File>
              <FileName>app_main.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_main.c</FilePath>
            </File>
            <File>
              <FileName>alg_diag.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\alg_diag.c</FilePath>
            </File>
            <File>
              <FileName>app_diag.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_diag.c</FilePath>
            </File>
            <File>
              <FileName>drv_io.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\drv_io.c</FilePath>
            </File>
            <File>
              <FileName>drv_hal_main.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\drv_hal_main.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_msp.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\stm32g4xx_hal_msp.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_it.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\stm32g4xx_it.c</FilePath>
            </File>
            <File>
              <FileName>alg_dio_edge.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\alg_dio_edge.c</FilePath>
            </File>
            <File>
              <FileName>alg_pcmaster.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\alg_pcmaster.c</FilePath>
            </File>
            <File>
              <FileName>alg_toolbox.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\alg_toolbox.c</FilePath>
            </File>
            <File>
              <FileName>alg_uapp.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\alg_uapp.c</FilePath>
            </File>
            <File>
              <FileName>app_cmd.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_cmd.c</FilePath>
            </File>
            <File>
              <FileName>app_pcmaster.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_pcmaster.c</FilePath>
            </File>
            <File>
              <FileName>app_record.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_record.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>2</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>1</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <uGnu>2</uGnu>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>2</vShortEn>
                    <vShortWch>2</vShortWch>
                    <v6Lto>2</v6Lto>
                    <v6WtE>2</v6WtE>
                    <v6Rtti>2</v6Rtti>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>app_test.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_test.c</FilePath>
            </File>
            <File>
              <FileName>drv_fal_flash_stm32g4_port.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\drv_fal_flash_stm32g4_port.c</FilePath>
            </File>
            <File>
              <FileName>drv_rs485.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\drv_rs485.c</FilePath>
            </File>
            <File>
              <FileName>drv_fdcan.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\drv_fdcan.c</FilePath>
            </File>
            <File>
              <FileName>alg_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\alg_crc.c</FilePath>
            </File>
            <File>
              <FileName>alg_modbus_common.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\alg_modbus_common.c</FilePath>
            </File>
            <File>
              <FileName>app_ad.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_ad.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>2</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>1</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <uGnu>2</uGnu>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>2</vShortEn>
                    <vShortWch>2</vShortWch>
                    <v6Lto>2</v6Lto>
                    <v6WtE>2</v6WtE>
                    <v6Rtti>2</v6Rtti>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>alg_ntc_convert.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\alg_ntc_convert.c</FilePath>
            </File>
            <File>
              <FileName>app_alternate_comm.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_alternate_comm.c</FilePath>
            </File>
            <File>
              <FileName>drv_soft_iic_hdc1080.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\drv_soft_iic_hdc1080.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>2</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>1</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <uGnu>2</uGnu>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>2</vShortEn>
                    <vShortWch>2</vShortWch>
                    <v6Lto>2</v6Lto>
                    <v6WtE>2</v6WtE>
                    <v6Rtti>2</v6Rtti>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>app_dpll3p.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_dpll3p.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>foc/inovance</GroupName>
          <Files>
            <File>
              <FileName>user_mcsdk_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\user_mcsdk_task.c</FilePath>
            </File>
            <File>
              <FileName>arm_abs_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_abs_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_abs_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_abs_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_common_tables.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_common_tables.c</FilePath>
            </File>
            <File>
              <FileName>arm_cos_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_cos_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cos_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_cos_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cos_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_cos_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_float_to_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_float_to_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_q15_to_float.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_q15_to_float.c</FilePath>
            </File>
            <File>
              <FileName>arm_q15_to_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_q15_to_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_q31_to_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_q31_to_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_cos_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_sin_cos_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_cos_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_sin_cos_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_sin_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_sin_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_sin_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_sqrt_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_sqrt_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_sqrt_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_sqrt_q31.c</FilePath>
            </File>
            <File>
              <FileName>DeviceInit.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\DeviceInit.c</FilePath>
            </File>
            <File>
              <FileName>f_comm.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_comm.c</FilePath>
            </File>
            <File>
              <FileName>f_error.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_error.c</FilePath>
            </File>
            <File>
              <FileName>f_frqSrc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_frqSrc.c</FilePath>
            </File>
            <File>
              <FileName>f_funcCode.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_funcCode.c</FilePath>
            </File>
            <File>
              <FileName>f_interface.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_interface.c</FilePath>
            </File>
            <File>
              <FileName>f_main.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_main.c</FilePath>
            </File>
            <File>
              <FileName>f_runSrc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_runSrc.c</FilePath>
            </File>
            <File>
              <FileName>f_runSrc_accDecFrq.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_runSrc_accDecFrq.c</FilePath>
            </File>
            <File>
              <FileName>MotorCarrier.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorCarrier.c</FilePath>
            </File>
            <File>
              <FileName>MotorConst.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorConst.c</FilePath>
            </File>
            <File>
              <FileName>MotorCurrentTransform.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorCurrentTransform.c</FilePath>
            </File>
            <File>
              <FileName>MotorDataExchange.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorDataExchange.c</FilePath>
            </File>
            <File>
              <FileName>MotorEncoder.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorEncoder.c</FilePath>
            </File>
            <File>
              <FileName>MotorInfoCollect.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorInfoCollect.c</FilePath>
            </File>
            <File>
              <FileName>MotorInvProtect.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorInvProtect.c</FilePath>
            </File>
            <File>
              <FileName>MotorIPMSVC.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorIPMSVC.c</FilePath>
            </File>
            <File>
              <FileName>MotorMain.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorMain.c</FilePath>
            </File>
            <File>
              <FileName>MotorParChange.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorParChange.c</FilePath>
            </File>
            <File>
              <FileName>MotorParEst.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorParEst.c</FilePath>
            </File>
            <File>
              <FileName>MotorPmsmMain.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorPmsmMain.c</FilePath>
            </File>
            <File>
              <FileName>MotorPmsmParEst.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorPmsmParEst.c</FilePath>
            </File>
            <File>
              <FileName>MotorPublicCal.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorPublicCal.c</FilePath>
            </File>
            <File>
              <FileName>MotorPWM.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorPWM.c</FilePath>
            </File>
            <File>
              <FileName>MotorSpeed.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorSpeed.c</FilePath>
            </File>
            <File>
              <FileName>MotorVar.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorVar.c</FilePath>
            </File>
            <File>
              <FileName>MotorVCMain.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorVCMain.c</FilePath>
            </File>
            <File>
              <FileName>MotorVF.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorVF.c</FilePath>
            </File>
            <File>
              <FileName>Scope.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\Scope.c</FilePath>
            </File>
            <File>
              <FileName>SubPrg.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\SubPrg.c</FilePath>
            </File>
            <File>
              <FileName>Svpwm.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\Svpwm.c</FilePath>
            </File>
            <File>
              <FileName>testApp.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\testApp.c</FilePath>
            </File>
            <File>
              <FileName>MotorFlyingStart.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorFlyingStart.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>protocol_comm</GroupName>
          <Files>
            <File>
              <FileName>app_bsm_com_outernet.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsmcomm_protocol\app_bsm_com_outernet.c</FilePath>
            </File>
            <File>
              <FileName>api_bsm_com_protocol.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsmcomm_protocol\api_bsm_com_protocol.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Drivers</GroupName>
          <Files>
            <File>
              <FileName>rtconfig.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\rtconfig.h</FilePath>
            </File>
            <File>
              <FileName>startup_stm32g474xx.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\CMSIS\Device\ST\STM32G4xx\Source\Templates\arm\startup_stm32g474xx.s</FilePath>
            </File>
            <File>
              <FileName>board.c</FileName>
              <FileType>1</FileType>
              <FilePath>board\board.c</FilePath>
            </File>
            <File>
              <FileName>drv_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\HAL_Drivers\drv_gpio.c</FilePath>
            </File>
            <File>
              <FileName>drv_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\HAL_Drivers\drv_spi.c</FilePath>
            </File>
            <File>
              <FileName>drv_crypto.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\HAL_Drivers\drv_crypto.c</FilePath>
            </File>
            <File>
              <FileName>drv_wdt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\HAL_Drivers\drv_wdt.c</FilePath>
            </File>
            <File>
              <FileName>drv_usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\HAL_Drivers\drv_usart.c</FilePath>
            </File>
            <File>
              <FileName>drv_common.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\HAL_Drivers\drv_common.c</FilePath>
            </File>
            <File>
              <FileName>drv_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\HAL_Drivers\drv_rtc.c</FilePath>
            </File>
            <File>
              <FileName>cpuusage.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\examples\kernel\cpuusage.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Applications</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>applications\main.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>CPU</GroupName>
          <Files>
            <File>
              <FileName>div0.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\libcpu\arm\common\div0.c</FilePath>
            </File>
            <File>
              <FileName>backtrace.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\libcpu\arm\common\backtrace.c</FilePath>
            </File>
            <File>
              <FileName>showmem.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\libcpu\arm\common\showmem.c</FilePath>
            </File>
            <File>
              <FileName>cpuport.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\libcpu\arm\cortex-m4\cpuport.c</FilePath>
            </File>
            <File>
              <FileName>context_rvds.S</FileName>
              <FileType>2</FileType>
              <FilePath>..\..\..\libcpu\arm\cortex-m4\context_rvds.S</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>DeviceDrivers</GroupName>
          <GroupOption>
            <CommonProperty>
              <UseCPPCompiler>0</UseCPPCompiler>
              <RVCTCodeConst>0</RVCTCodeConst>
              <RVCTZI>0</RVCTZI>
              <RVCTOtherData>0</RVCTOtherData>
              <ModuleSelection>0</ModuleSelection>
              <IncludeInBuild>1</IncludeInBuild>
              <AlwaysBuild>0</AlwaysBuild>
              <GenerateAssemblyFile>0</GenerateAssemblyFile>
              <AssembleAssemblyFile>0</AssembleAssemblyFile>
              <PublicsOnly>0</PublicsOnly>
              <StopOnExitCode>3</StopOnExitCode>
              <CustomArgument></CustomArgument>
              <IncludeLibraryModules></IncludeLibraryModules>
              <ComprImg>0</ComprImg>
            </CommonProperty>
            <GroupArmAds>
              <Cads>
                <interw>2</interw>
                <Optim>0</Optim>
                <oTime>2</oTime>
                <SplitLS>2</SplitLS>
                <OneElfS>2</OneElfS>
                <Strict>2</Strict>
                <EnumInt>2</EnumInt>
                <PlainCh>2</PlainCh>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <wLevel>0</wLevel>
                <uThumb>2</uThumb>
                <uSurpInc>2</uSurpInc>
                <uC99>2</uC99>
                <uGnu>2</uGnu>
                <useXO>2</useXO>
                <v6Lang>0</v6Lang>
                <v6LangP>0</v6LangP>
                <vShortEn>2</vShortEn>
                <vShortWch>2</vShortWch>
                <v6Lto>2</v6Lto>
                <v6WtE>2</v6WtE>
                <v6Rtti>2</v6Rtti>
                <VariousControls>
                  <MiscControls> </MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Cads>
              <Aads>
                <interw>2</interw>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <thumb>2</thumb>
                <SplitLS>2</SplitLS>
                <SwStkChk>2</SwStkChk>
                <NoWarn>2</NoWarn>
                <uSurpInc>2</uSurpInc>
                <useXO>2</useXO>
                <ClangAsOpt>0</ClangAsOpt>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Aads>
            </GroupArmAds>
          </GroupOption>
          <Files>
            <File>
              <FileName>hwcrypto.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\hwcrypto\hwcrypto.c</FilePath>
            </File>
            <File>
              <FileName>pin.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\misc\pin.c</FilePath>
            </File>
            <File>
              <FileName>mtd_nor.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\mtd\mtd_nor.c</FilePath>
            </File>
            <File>
              <FileName>soft_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\rtc\soft_rtc.c</FilePath>
            </File>
            <File>
              <FileName>rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\rtc\rtc.c</FilePath>
            </File>
            <File>
              <FileName>serial.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\serial\serial.c</FilePath>
            </File>
            <File>
              <FileName>sfud.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\spi\sfud\src\sfud.c</FilePath>
            </File>
            <File>
              <FileName>spi_flash_sfud.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\spi\spi_flash_sfud.c</FilePath>
            </File>
            <File>
              <FileName>sfud_sfdp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\spi\sfud\src\sfud_sfdp.c</FilePath>
            </File>
            <File>
              <FileName>spi_core.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\spi\spi_core.c</FilePath>
            </File>
            <File>
              <FileName>spi_dev.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\spi\spi_dev.c</FilePath>
            </File>
            <File>
              <FileName>workqueue.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\src\workqueue.c</FilePath>
            </File>
            <File>
              <FileName>completion.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\src\completion.c</FilePath>
            </File>
            <File>
              <FileName>dataqueue.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\src\dataqueue.c</FilePath>
            </File>
            <File>
              <FileName>pipe.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\src\pipe.c</FilePath>
            </File>
            <File>
              <FileName>ringblk_buf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\src\ringblk_buf.c</FilePath>
            </File>
            <File>
              <FileName>waitqueue.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\src\waitqueue.c</FilePath>
            </File>
            <File>
              <FileName>ringbuffer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\src\ringbuffer.c</FilePath>
            </File>
            <File>
              <FileName>watchdog.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\watchdog\watchdog.c</FilePath>
            </File>
            <File>
              <FileName>sensor.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\sensors\sensor.c</FilePath>
            </File>
            <File>
              <FileName>sensor_cmd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\sensors\sensor_cmd.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>fal</GroupName>
          <GroupOption>
            <CommonProperty>
              <UseCPPCompiler>0</UseCPPCompiler>
              <RVCTCodeConst>0</RVCTCodeConst>
              <RVCTZI>0</RVCTZI>
              <RVCTOtherData>0</RVCTOtherData>
              <ModuleSelection>0</ModuleSelection>
              <IncludeInBuild>1</IncludeInBuild>
              <AlwaysBuild>0</AlwaysBuild>
              <GenerateAssemblyFile>0</GenerateAssemblyFile>
              <AssembleAssemblyFile>0</AssembleAssemblyFile>
              <PublicsOnly>0</PublicsOnly>
              <StopOnExitCode>3</StopOnExitCode>
              <CustomArgument></CustomArgument>
              <IncludeLibraryModules></IncludeLibraryModules>
              <ComprImg>0</ComprImg>
            </CommonProperty>
            <GroupArmAds>
              <Cads>
                <interw>2</interw>
                <Optim>0</Optim>
                <oTime>2</oTime>
                <SplitLS>2</SplitLS>
                <OneElfS>2</OneElfS>
                <Strict>2</Strict>
                <EnumInt>2</EnumInt>
                <PlainCh>2</PlainCh>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <wLevel>0</wLevel>
                <uThumb>2</uThumb>
                <uSurpInc>2</uSurpInc>
                <uC99>2</uC99>
                <uGnu>2</uGnu>
                <useXO>2</useXO>
                <v6Lang>0</v6Lang>
                <v6LangP>0</v6LangP>
                <vShortEn>2</vShortEn>
                <vShortWch>2</vShortWch>
                <v6Lto>2</v6Lto>
                <v6WtE>2</v6WtE>
                <v6Rtti>2</v6Rtti>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define> </Define>
                  <Undefine> </Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Cads>
              <Aads>
                <interw>2</interw>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <thumb>2</thumb>
                <SplitLS>2</SplitLS>
                <SwStkChk>2</SwStkChk>
                <NoWarn>2</NoWarn>
                <uSurpInc>2</uSurpInc>
                <useXO>2</useXO>
                <ClangAsOpt>0</ClangAsOpt>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Aads>
            </GroupArmAds>
          </GroupOption>
          <Files>
            <File>
              <FileName>fal_rtt.c</FileName>
              <FileType>1</FileType>
              <FilePath>packages\fal-v0.5.0\src\fal_rtt.c</FilePath>
            </File>
            <File>
              <FileName>fal.c</FileName>
              <FileType>1</FileType>
              <FilePath>packages\fal-v0.5.0\src\fal.c</FilePath>
            </File>
            <File>
              <FileName>fal_flash_sfud_port.c</FileName>
              <FileType>1</FileType>
              <FilePath>packages\fal-v0.5.0\samples\porting\fal_flash_sfud_port.c</FilePath>
            </File>
            <File>
              <FileName>fal_partition.c</FileName>
              <FileType>1</FileType>
              <FilePath>packages\fal-v0.5.0\src\fal_partition.c</FilePath>
            </File>
            <File>
              <FileName>fal_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>packages\fal-v0.5.0\src\fal_flash.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Filesystem</GroupName>
          <Files>
            <File>
              <FileName>dfs_posix.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\dfs\src\dfs_posix.c</FilePath>
            </File>
            <File>
              <FileName>dfs_fs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\dfs\src\dfs_fs.c</FilePath>
            </File>
            <File>
              <FileName>dfs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\dfs\src\dfs.c</FilePath>
            </File>
            <File>
              <FileName>dfs_file.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\dfs\src\dfs_file.c</FilePath>
            </File>
            <File>
              <FileName>devfs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\dfs\filesystems\devfs\devfs.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Finsh</GroupName>
          <Files>
            <File>
              <FileName>shell.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\finsh\shell.c</FilePath>
            </File>
            <File>
              <FileName>msh.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\finsh\msh.c</FilePath>
            </File>
            <File>
              <FileName>msh_file.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\finsh\msh_file.c</FilePath>
            </File>
            <File>
              <FileName>cmd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\finsh\cmd.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Kernel</GroupName>
          <Files>
            <File>
              <FileName>mem.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\mem.c</FilePath>
            </File>
            <File>
              <FileName>scheduler.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\scheduler.c</FilePath>
            </File>
            <File>
              <FileName>idle.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\idle.c</FilePath>
            </File>
            <File>
              <FileName>timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\timer.c</FilePath>
            </File>
            <File>
              <FileName>thread.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\thread.c</FilePath>
            </File>
            <File>
              <FileName>device.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\device.c</FilePath>
            </File>
            <File>
              <FileName>mempool.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\mempool.c</FilePath>
            </File>
            <File>
              <FileName>irq.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\irq.c</FilePath>
            </File>
            <File>
              <FileName>kservice.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\kservice.c</FilePath>
            </File>
            <File>
              <FileName>clock.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\clock.c</FilePath>
            </File>
            <File>
              <FileName>object.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\object.c</FilePath>
            </File>
            <File>
              <FileName>components.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\components.c</FilePath>
            </File>
            <File>
              <FileName>ipc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\ipc.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>libc</GroupName>
          <Files>
            <File>
              <FileName>time.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\libc\compilers\common\time.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Libraries</GroupName>
          <Files>
            <File>
              <FileName>stm32g4xx_hal_dma_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_dma_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_gpio.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_rcc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rcc_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_iwdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_iwdg.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_adc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_adc_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rtc.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_uart_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_uart_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_rtc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rtc_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_tim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_tim.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_cryp_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_cryp_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_pwr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_pwr.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_dac_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_dac_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_flash_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_flash_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_fdcan.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_fdcan.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_cryp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_cryp.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_usart_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_usart_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_dac.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_dac.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_rng.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rng.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_tim_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_tim_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_qspi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_qspi.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_wwdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_wwdg.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_usart.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_cortex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_cortex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_rcc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rcc.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_dma.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_uart.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_flash.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_pwr_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_pwr_ex.c</FilePath>
            </File>
            <File>
              <FileName>system_stm32g4xx.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\CMSIS\Device\ST\STM32G4xx\Source\Templates\system_stm32g4xx.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_spi.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_adc.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_nor.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_nor.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_crc.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_cordic.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_cordic.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_ll_cordic.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_ll_cordic.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_ll_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_ll_gpio.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>littlefs</GroupName>
          <Files>
            <File>
              <FileName>dfs_lfs.c</FileName>
              <FileType>1</FileType>
              <FilePath>packages\littlefs-latest\dfs_lfs.c</FilePath>
            </File>
            <File>
              <FileName>lfs_util.c</FileName>
              <FileType>1</FileType>
              <FilePath>packages\littlefs-latest\lfs_util.c</FilePath>
            </File>
            <File>
              <FileName>lfs.c</FileName>
              <FileType>1</FileType>
              <FilePath>packages\littlefs-latest\lfs.c</FilePath>
            </File>
            <File>
              <FileName>lfs_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>packages\littlefs-latest\lfs_crc.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>SAL</GroupName>
          <GroupOption>
            <CommonProperty>
              <UseCPPCompiler>0</UseCPPCompiler>
              <RVCTCodeConst>0</RVCTCodeConst>
              <RVCTZI>0</RVCTZI>
              <RVCTOtherData>0</RVCTOtherData>
              <ModuleSelection>0</ModuleSelection>
              <IncludeInBuild>0</IncludeInBuild>
              <AlwaysBuild>0</AlwaysBuild>
              <GenerateAssemblyFile>2</GenerateAssemblyFile>
              <AssembleAssemblyFile>2</AssembleAssemblyFile>
              <PublicsOnly>2</PublicsOnly>
              <StopOnExitCode>11</StopOnExitCode>
              <CustomArgument></CustomArgument>
              <IncludeLibraryModules></IncludeLibraryModules>
              <ComprImg>1</ComprImg>
            </CommonProperty>
            <GroupArmAds>
              <Cads>
                <interw>2</interw>
                <Optim>0</Optim>
                <oTime>2</oTime>
                <SplitLS>2</SplitLS>
                <OneElfS>2</OneElfS>
                <Strict>2</Strict>
                <EnumInt>2</EnumInt>
                <PlainCh>2</PlainCh>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <wLevel>0</wLevel>
                <uThumb>2</uThumb>
                <uSurpInc>2</uSurpInc>
                <uC99>2</uC99>
                <uGnu>2</uGnu>
                <useXO>2</useXO>
                <v6Lang>0</v6Lang>
                <v6LangP>0</v6LangP>
                <vShortEn>2</vShortEn>
                <vShortWch>2</vShortWch>
                <v6Lto>2</v6Lto>
                <v6WtE>2</v6WtE>
                <v6Rtti>2</v6Rtti>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Cads>
              <Aads>
                <interw>2</interw>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <thumb>2</thumb>
                <SplitLS>2</SplitLS>
                <SwStkChk>2</SwStkChk>
                <NoWarn>2</NoWarn>
                <uSurpInc>2</uSurpInc>
                <useXO>2</useXO>
                <ClangAsOpt>0</ClangAsOpt>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Aads>
            </GroupArmAds>
          </GroupOption>
          <Files>
            <File>
              <FileName>netdev_ipaddr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\net\netdev\src\netdev_ipaddr.c</FilePath>
            </File>
            <File>
              <FileName>netdev.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\net\netdev\src\netdev.c</FilePath>
            </File>
            <File>
              <FileName>sal_socket.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\net\sal_socket\src\sal_socket.c</FilePath>
            </File>
            <File>
              <FileName>net_netdb.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\net\sal_socket\socket\net_netdb.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Utilities</GroupName>
          <Files>
            <File>
              <FileName>ulog.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\utilities\ulog\ulog.c</FilePath>
            </File>
            <File>
              <FileName>console_be.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\utilities\ulog\backend\console_be.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>FlashDB</GroupName>
          <Files>
            <File>
              <FileName>fdb_cmd.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\FlashDB-v1.1.0\src\fdb_cmd.c</FilePath>
            </File>
            <File>
              <FileName>fdb.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\FlashDB-v1.1.0\src\fdb.c</FilePath>
            </File>
            <File>
              <FileName>fdb_kvdb.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\FlashDB-v1.1.0\src\fdb_kvdb.c</FilePath>
            </File>
            <File>
              <FileName>fdb_tsdb.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\FlashDB-v1.1.0\src\fdb_tsdb.c</FilePath>
            </File>
            <File>
              <FileName>fdb_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\FlashDB-v1.1.0\src\fdb_utils.c</FilePath>
            </File>
            <File>
              <FileName>kvdb_basic_sample.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\FlashDB-v1.1.0\samples\kvdb_basic_sample.c</FilePath>
            </File>
            <File>
              <FileName>tsdb_sample.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\FlashDB-v1.1.0\samples\tsdb_sample.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>lid3dh</GroupName>
          <Files>
            <File>
              <FileName>lis3dh_reg.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\lis3dh\lis3dh_reg.c</FilePath>
            </File>
            <File>
              <FileName>port_sensor.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\lis3dh\port_sensor.c</FilePath>
            </File>
            <File>
              <FileName>sensor_st_lis3dh.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\lis3dh\sensor_st_lis3dh.c</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
    <Target>
      <TargetName>EMC型式试验</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>6160000::V6.16::ARMCLANG</pCCUsed>
      <uAC6>1</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>STM32G474RETx</Device>
          <Vendor>STMicroelectronics</Vendor>
          <PackID>Keil.STM32G4xx_DFP.1.2.0</PackID>
          <PackURL>http://www.keil.com/pack/</PackURL>
          <Cpu>IRAM(0x20000000,0x00020000) IROM(0x08000000,0x00080000) CPUTYPE("Cortex-M4") FPU2 CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN1 -FF0STM32G4xx_512_Dual -********** -FL080000 -FP0($$Device:STM32G474RETx$CMSIS\Flash\STM32G4xx_512_Dual.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:STM32G474RETx$Drivers\CMSIS\Device\ST\STM32G4xx\Include\stm32g4xx.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:STM32G474RETx$CMSIS\SVD\STM32G474xx.svd</SFDFile>
          <bCustSvd>1</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>..\..\..\..\build\inv\</OutputDirectory>
          <OutputName>PMSM12_Test_A03_T</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>..\..\..\..\build\inv\list\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>fromelf --bin !L --output ./obj/@H.bin</UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> -REMAP -MPU</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> -MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3></Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>0</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x80000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x80000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>2</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>1</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>3</v6Lang>
            <v6LangP>5</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>HW_PVPB08,NOVFD_TEST_DEBUG,NOUSE_MCSDK_UART,ICS_SENSORS,USE_HAL_DRIVER, LFS_CONFIG=lfs_config.h, __RTTHREAD__, STM32G474xx, __CLK_TCK=RT_TICK_PER_SECOND,ARM_MATH_CM4</Define>
              <Undefine></Undefine>
              <IncludePath>applications;.;..\..\..\components\net\at\include;..\..\..\components\net\at\at_socket;packages\at_device-latest\inc;packages\at_device-latest\class\esp8266;..\..\..\libcpu\arm\common;..\..\..\libcpu\arm\cortex-m4;..\..\..\components\drivers\hwcrypto;.;..\..\..\components\drivers\include;..\..\..\components\drivers\include;..\..\..\components\drivers\include;..\..\..\components\drivers\include;..\..\..\components\drivers\spi;..\..\..\components\drivers\include;..\..\..\components\drivers\spi\sfud\inc;..\..\..\components\drivers\include;..\..\..\components\drivers\include;board;board\CubeMX_Config\Inc;..\libraries\HAL_Drivers;..\libraries\HAL_Drivers\config;packages\fal-v0.5.0\inc;..\..\..\components\dfs\include;..\..\..\components\dfs\filesystems\devfs;..\..\..\components\finsh;.;..\..\..\include;..\..\..\components\libc\compilers\common;..\..\..\components\libc\compilers\common\nogcc;..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Inc;..\libraries\STM32G4xx_HAL\CMSIS\Device\ST\STM32G4xx\Include;..\libraries\STM32G4xx_HAL\CMSIS\Include;packages\littlefs-latest;packages\netutils-v1.3.1\ntp;..\..\..\components\net\netdev\include;..\..\..\components\net\sal_socket\include;..\..\..\components\net\sal_socket\include\socket;..\..\..\components\net\sal_socket\impl;..\..\..\components\net\sal_socket\include\socket\sys_socket;..\..\..\examples\utest\testcases\kernel;..\..\..\components\utilities\ulog;.\packages\FlashDB-v1.1.0\inc;packages\freemodbus-latest\modbus\include;packages\freemodbus-latest\modbus\rtu;packages\freemodbus-latest\modbus\ascii;packages\freemodbus-latest\modbus\tcp;packages\freemodbus-latest\port;.\BsmProtocol;.\CRC;..\libraries\STM32G4xx_HAL\CMSIS\DSP\Include;.\user\inc;.\framework_foc\acim_svpwm;.\framework_pfc;.\framework_boost;.\bsmcomm_protocol;.\customer_port\merak;..\..\..\components\drivers\include\drivers;.\packages\lis3dh;.\framework_foc\pmsm_inovance\Core\Inc;.\framework_foc\pmsm_inovance\Core\Inc\INC_USER;.\framework_foc\pmsm_inovance\Core\Src\userApps;.\framework_foc\pmsm_inovance</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>4</ClangAsOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>0</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x08000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile>.\board\linker_scripts\link.sct</ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>customer/yingsai</GroupName>
          <Files>
            <File>
              <FileName>customer_port.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\customer_port\merak\customer_port.h</FilePath>
            </File>
            <File>
              <FileName>app_can_port.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\customer_port\merak\app_can_port.c</FilePath>
            </File>
            <File>
              <FileName>app_rs485_port.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\customer_port\merak\app_rs485_port.c</FilePath>
            </File>
            <File>
              <FileName>customer_port.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\customer_port\merak\customer_port.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>user</GroupName>
          <GroupOption>
            <CommonProperty>
              <UseCPPCompiler>0</UseCPPCompiler>
              <RVCTCodeConst>0</RVCTCodeConst>
              <RVCTZI>0</RVCTZI>
              <RVCTOtherData>0</RVCTOtherData>
              <ModuleSelection>0</ModuleSelection>
              <IncludeInBuild>1</IncludeInBuild>
              <AlwaysBuild>0</AlwaysBuild>
              <GenerateAssemblyFile>0</GenerateAssemblyFile>
              <AssembleAssemblyFile>0</AssembleAssemblyFile>
              <PublicsOnly>0</PublicsOnly>
              <StopOnExitCode>3</StopOnExitCode>
              <CustomArgument></CustomArgument>
              <IncludeLibraryModules></IncludeLibraryModules>
              <ComprImg>0</ComprImg>
            </CommonProperty>
            <GroupArmAds>
              <Cads>
                <interw>2</interw>
                <Optim>0</Optim>
                <oTime>2</oTime>
                <SplitLS>2</SplitLS>
                <OneElfS>2</OneElfS>
                <Strict>2</Strict>
                <EnumInt>2</EnumInt>
                <PlainCh>2</PlainCh>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <wLevel>0</wLevel>
                <uThumb>2</uThumb>
                <uSurpInc>2</uSurpInc>
                <uC99>2</uC99>
                <uGnu>2</uGnu>
                <useXO>2</useXO>
                <v6Lang>0</v6Lang>
                <v6LangP>0</v6LangP>
                <vShortEn>2</vShortEn>
                <vShortWch>2</vShortWch>
                <v6Lto>2</v6Lto>
                <v6WtE>2</v6WtE>
                <v6Rtti>2</v6Rtti>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define> </Define>
                  <Undefine> </Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Cads>
              <Aads>
                <interw>2</interw>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <thumb>2</thumb>
                <SplitLS>2</SplitLS>
                <SwStkChk>2</SwStkChk>
                <NoWarn>2</NoWarn>
                <uSurpInc>2</uSurpInc>
                <useXO>2</useXO>
                <ClangAsOpt>0</ClangAsOpt>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Aads>
            </GroupArmAds>
          </GroupOption>
          <Files>
            <File>
              <FileName>uapp.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\user\inc\uapp.h</FilePath>
            </File>
            <File>
              <FileName>app_user.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_user.c</FilePath>
            </File>
            <File>
              <FileName>app_sub.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_sub.c</FilePath>
            </File>
            <File>
              <FileName>app_main.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_main.c</FilePath>
            </File>
            <File>
              <FileName>alg_diag.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\alg_diag.c</FilePath>
            </File>
            <File>
              <FileName>app_diag.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_diag.c</FilePath>
            </File>
            <File>
              <FileName>drv_io.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\drv_io.c</FilePath>
            </File>
            <File>
              <FileName>drv_hal_main.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\drv_hal_main.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_msp.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\stm32g4xx_hal_msp.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_it.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\stm32g4xx_it.c</FilePath>
            </File>
            <File>
              <FileName>alg_dio_edge.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\alg_dio_edge.c</FilePath>
            </File>
            <File>
              <FileName>alg_pcmaster.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\alg_pcmaster.c</FilePath>
            </File>
            <File>
              <FileName>alg_toolbox.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\alg_toolbox.c</FilePath>
            </File>
            <File>
              <FileName>alg_uapp.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\alg_uapp.c</FilePath>
            </File>
            <File>
              <FileName>app_cmd.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_cmd.c</FilePath>
            </File>
            <File>
              <FileName>app_pcmaster.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_pcmaster.c</FilePath>
            </File>
            <File>
              <FileName>app_record.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_record.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>2</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>1</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <uGnu>2</uGnu>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>2</vShortEn>
                    <vShortWch>2</vShortWch>
                    <v6Lto>2</v6Lto>
                    <v6WtE>2</v6WtE>
                    <v6Rtti>2</v6Rtti>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>app_test.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_test.c</FilePath>
            </File>
            <File>
              <FileName>drv_fal_flash_stm32g4_port.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\drv_fal_flash_stm32g4_port.c</FilePath>
            </File>
            <File>
              <FileName>drv_rs485.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\drv_rs485.c</FilePath>
            </File>
            <File>
              <FileName>drv_fdcan.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\drv_fdcan.c</FilePath>
            </File>
            <File>
              <FileName>alg_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\alg_crc.c</FilePath>
            </File>
            <File>
              <FileName>alg_modbus_common.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\alg_modbus_common.c</FilePath>
            </File>
            <File>
              <FileName>app_ad.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_ad.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>2</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>1</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <uGnu>2</uGnu>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>2</vShortEn>
                    <vShortWch>2</vShortWch>
                    <v6Lto>2</v6Lto>
                    <v6WtE>2</v6WtE>
                    <v6Rtti>2</v6Rtti>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>alg_ntc_convert.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\alg_ntc_convert.c</FilePath>
            </File>
            <File>
              <FileName>app_alternate_comm.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_alternate_comm.c</FilePath>
            </File>
            <File>
              <FileName>drv_soft_iic_hdc1080.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\drv_soft_iic_hdc1080.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>2</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>1</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <uGnu>2</uGnu>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>2</vShortEn>
                    <vShortWch>2</vShortWch>
                    <v6Lto>2</v6Lto>
                    <v6WtE>2</v6WtE>
                    <v6Rtti>2</v6Rtti>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>app_dpll3p.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\user\app_dpll3p.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>foc/inovance</GroupName>
          <Files>
            <File>
              <FileName>user_mcsdk_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\user_mcsdk_task.c</FilePath>
            </File>
            <File>
              <FileName>arm_abs_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_abs_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_abs_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_abs_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_common_tables.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_common_tables.c</FilePath>
            </File>
            <File>
              <FileName>arm_cos_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_cos_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_cos_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_cos_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_cos_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_cos_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_float_to_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_float_to_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_q15_to_float.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_q15_to_float.c</FilePath>
            </File>
            <File>
              <FileName>arm_q15_to_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_q15_to_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_q31_to_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_q31_to_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_cos_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_sin_cos_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_cos_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_sin_cos_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_f32.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_sin_f32.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_sin_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_sin_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_sin_q31.c</FilePath>
            </File>
            <File>
              <FileName>arm_sqrt_q15.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_sqrt_q15.c</FilePath>
            </File>
            <File>
              <FileName>arm_sqrt_q31.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\arm_sqrt_q31.c</FilePath>
            </File>
            <File>
              <FileName>DeviceInit.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\DeviceInit.c</FilePath>
            </File>
            <File>
              <FileName>f_comm.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_comm.c</FilePath>
            </File>
            <File>
              <FileName>f_error.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_error.c</FilePath>
            </File>
            <File>
              <FileName>f_frqSrc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_frqSrc.c</FilePath>
            </File>
            <File>
              <FileName>f_funcCode.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_funcCode.c</FilePath>
            </File>
            <File>
              <FileName>f_interface.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_interface.c</FilePath>
            </File>
            <File>
              <FileName>f_main.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_main.c</FilePath>
            </File>
            <File>
              <FileName>f_runSrc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_runSrc.c</FilePath>
            </File>
            <File>
              <FileName>f_runSrc_accDecFrq.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\f_runSrc_accDecFrq.c</FilePath>
            </File>
            <File>
              <FileName>MotorCarrier.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorCarrier.c</FilePath>
            </File>
            <File>
              <FileName>MotorConst.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorConst.c</FilePath>
            </File>
            <File>
              <FileName>MotorCurrentTransform.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorCurrentTransform.c</FilePath>
            </File>
            <File>
              <FileName>MotorDataExchange.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorDataExchange.c</FilePath>
            </File>
            <File>
              <FileName>MotorEncoder.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorEncoder.c</FilePath>
            </File>
            <File>
              <FileName>MotorInfoCollect.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorInfoCollect.c</FilePath>
            </File>
            <File>
              <FileName>MotorInvProtect.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorInvProtect.c</FilePath>
            </File>
            <File>
              <FileName>MotorIPMSVC.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorIPMSVC.c</FilePath>
            </File>
            <File>
              <FileName>MotorMain.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorMain.c</FilePath>
            </File>
            <File>
              <FileName>MotorParChange.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorParChange.c</FilePath>
            </File>
            <File>
              <FileName>MotorParEst.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorParEst.c</FilePath>
            </File>
            <File>
              <FileName>MotorPmsmMain.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorPmsmMain.c</FilePath>
            </File>
            <File>
              <FileName>MotorPmsmParEst.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorPmsmParEst.c</FilePath>
            </File>
            <File>
              <FileName>MotorPublicCal.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorPublicCal.c</FilePath>
            </File>
            <File>
              <FileName>MotorPWM.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorPWM.c</FilePath>
            </File>
            <File>
              <FileName>MotorSpeed.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorSpeed.c</FilePath>
            </File>
            <File>
              <FileName>MotorVar.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorVar.c</FilePath>
            </File>
            <File>
              <FileName>MotorVCMain.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorVCMain.c</FilePath>
            </File>
            <File>
              <FileName>MotorVF.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorVF.c</FilePath>
            </File>
            <File>
              <FileName>Scope.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\Scope.c</FilePath>
            </File>
            <File>
              <FileName>SubPrg.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\SubPrg.c</FilePath>
            </File>
            <File>
              <FileName>Svpwm.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\Svpwm.c</FilePath>
            </File>
            <File>
              <FileName>testApp.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\testApp.c</FilePath>
            </File>
            <File>
              <FileName>MotorFlyingStart.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\framework_foc\pmsm_inovance\Core\Src\userApps\MotorFlyingStart.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>protocol_comm</GroupName>
          <Files>
            <File>
              <FileName>app_bsm_com_outernet.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsmcomm_protocol\app_bsm_com_outernet.c</FilePath>
            </File>
            <File>
              <FileName>api_bsm_com_protocol.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\bsmcomm_protocol\api_bsm_com_protocol.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Drivers</GroupName>
          <Files>
            <File>
              <FileName>rtconfig.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\rtconfig.h</FilePath>
            </File>
            <File>
              <FileName>startup_stm32g474xx.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\CMSIS\Device\ST\STM32G4xx\Source\Templates\arm\startup_stm32g474xx.s</FilePath>
            </File>
            <File>
              <FileName>board.c</FileName>
              <FileType>1</FileType>
              <FilePath>board\board.c</FilePath>
            </File>
            <File>
              <FileName>drv_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\HAL_Drivers\drv_gpio.c</FilePath>
            </File>
            <File>
              <FileName>drv_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\HAL_Drivers\drv_spi.c</FilePath>
            </File>
            <File>
              <FileName>drv_crypto.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\HAL_Drivers\drv_crypto.c</FilePath>
            </File>
            <File>
              <FileName>drv_wdt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\HAL_Drivers\drv_wdt.c</FilePath>
            </File>
            <File>
              <FileName>drv_usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\HAL_Drivers\drv_usart.c</FilePath>
            </File>
            <File>
              <FileName>drv_common.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\HAL_Drivers\drv_common.c</FilePath>
            </File>
            <File>
              <FileName>drv_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\HAL_Drivers\drv_rtc.c</FilePath>
            </File>
            <File>
              <FileName>cpuusage.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\examples\kernel\cpuusage.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Applications</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>applications\main.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>CPU</GroupName>
          <Files>
            <File>
              <FileName>div0.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\libcpu\arm\common\div0.c</FilePath>
            </File>
            <File>
              <FileName>backtrace.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\libcpu\arm\common\backtrace.c</FilePath>
            </File>
            <File>
              <FileName>showmem.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\libcpu\arm\common\showmem.c</FilePath>
            </File>
            <File>
              <FileName>cpuport.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\libcpu\arm\cortex-m4\cpuport.c</FilePath>
            </File>
            <File>
              <FileName>context_rvds.S</FileName>
              <FileType>2</FileType>
              <FilePath>..\..\..\libcpu\arm\cortex-m4\context_rvds.S</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>DeviceDrivers</GroupName>
          <GroupOption>
            <CommonProperty>
              <UseCPPCompiler>0</UseCPPCompiler>
              <RVCTCodeConst>0</RVCTCodeConst>
              <RVCTZI>0</RVCTZI>
              <RVCTOtherData>0</RVCTOtherData>
              <ModuleSelection>0</ModuleSelection>
              <IncludeInBuild>1</IncludeInBuild>
              <AlwaysBuild>0</AlwaysBuild>
              <GenerateAssemblyFile>0</GenerateAssemblyFile>
              <AssembleAssemblyFile>0</AssembleAssemblyFile>
              <PublicsOnly>0</PublicsOnly>
              <StopOnExitCode>3</StopOnExitCode>
              <CustomArgument></CustomArgument>
              <IncludeLibraryModules></IncludeLibraryModules>
              <ComprImg>0</ComprImg>
            </CommonProperty>
            <GroupArmAds>
              <Cads>
                <interw>2</interw>
                <Optim>0</Optim>
                <oTime>2</oTime>
                <SplitLS>2</SplitLS>
                <OneElfS>2</OneElfS>
                <Strict>2</Strict>
                <EnumInt>2</EnumInt>
                <PlainCh>2</PlainCh>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <wLevel>0</wLevel>
                <uThumb>2</uThumb>
                <uSurpInc>2</uSurpInc>
                <uC99>2</uC99>
                <uGnu>2</uGnu>
                <useXO>2</useXO>
                <v6Lang>0</v6Lang>
                <v6LangP>0</v6LangP>
                <vShortEn>2</vShortEn>
                <vShortWch>2</vShortWch>
                <v6Lto>2</v6Lto>
                <v6WtE>2</v6WtE>
                <v6Rtti>2</v6Rtti>
                <VariousControls>
                  <MiscControls> </MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Cads>
              <Aads>
                <interw>2</interw>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <thumb>2</thumb>
                <SplitLS>2</SplitLS>
                <SwStkChk>2</SwStkChk>
                <NoWarn>2</NoWarn>
                <uSurpInc>2</uSurpInc>
                <useXO>2</useXO>
                <ClangAsOpt>0</ClangAsOpt>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Aads>
            </GroupArmAds>
          </GroupOption>
          <Files>
            <File>
              <FileName>hwcrypto.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\hwcrypto\hwcrypto.c</FilePath>
            </File>
            <File>
              <FileName>pin.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\misc\pin.c</FilePath>
            </File>
            <File>
              <FileName>mtd_nor.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\mtd\mtd_nor.c</FilePath>
            </File>
            <File>
              <FileName>soft_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\rtc\soft_rtc.c</FilePath>
            </File>
            <File>
              <FileName>rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\rtc\rtc.c</FilePath>
            </File>
            <File>
              <FileName>serial.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\serial\serial.c</FilePath>
            </File>
            <File>
              <FileName>sfud.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\spi\sfud\src\sfud.c</FilePath>
            </File>
            <File>
              <FileName>spi_flash_sfud.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\spi\spi_flash_sfud.c</FilePath>
            </File>
            <File>
              <FileName>sfud_sfdp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\spi\sfud\src\sfud_sfdp.c</FilePath>
            </File>
            <File>
              <FileName>spi_core.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\spi\spi_core.c</FilePath>
            </File>
            <File>
              <FileName>spi_dev.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\spi\spi_dev.c</FilePath>
            </File>
            <File>
              <FileName>workqueue.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\src\workqueue.c</FilePath>
            </File>
            <File>
              <FileName>completion.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\src\completion.c</FilePath>
            </File>
            <File>
              <FileName>dataqueue.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\src\dataqueue.c</FilePath>
            </File>
            <File>
              <FileName>pipe.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\src\pipe.c</FilePath>
            </File>
            <File>
              <FileName>ringblk_buf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\src\ringblk_buf.c</FilePath>
            </File>
            <File>
              <FileName>waitqueue.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\src\waitqueue.c</FilePath>
            </File>
            <File>
              <FileName>ringbuffer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\src\ringbuffer.c</FilePath>
            </File>
            <File>
              <FileName>watchdog.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\watchdog\watchdog.c</FilePath>
            </File>
            <File>
              <FileName>sensor.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\sensors\sensor.c</FilePath>
            </File>
            <File>
              <FileName>sensor_cmd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\drivers\sensors\sensor_cmd.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>fal</GroupName>
          <GroupOption>
            <CommonProperty>
              <UseCPPCompiler>0</UseCPPCompiler>
              <RVCTCodeConst>0</RVCTCodeConst>
              <RVCTZI>0</RVCTZI>
              <RVCTOtherData>0</RVCTOtherData>
              <ModuleSelection>0</ModuleSelection>
              <IncludeInBuild>1</IncludeInBuild>
              <AlwaysBuild>0</AlwaysBuild>
              <GenerateAssemblyFile>0</GenerateAssemblyFile>
              <AssembleAssemblyFile>0</AssembleAssemblyFile>
              <PublicsOnly>0</PublicsOnly>
              <StopOnExitCode>3</StopOnExitCode>
              <CustomArgument></CustomArgument>
              <IncludeLibraryModules></IncludeLibraryModules>
              <ComprImg>0</ComprImg>
            </CommonProperty>
            <GroupArmAds>
              <Cads>
                <interw>2</interw>
                <Optim>0</Optim>
                <oTime>2</oTime>
                <SplitLS>2</SplitLS>
                <OneElfS>2</OneElfS>
                <Strict>2</Strict>
                <EnumInt>2</EnumInt>
                <PlainCh>2</PlainCh>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <wLevel>0</wLevel>
                <uThumb>2</uThumb>
                <uSurpInc>2</uSurpInc>
                <uC99>2</uC99>
                <uGnu>2</uGnu>
                <useXO>2</useXO>
                <v6Lang>0</v6Lang>
                <v6LangP>0</v6LangP>
                <vShortEn>2</vShortEn>
                <vShortWch>2</vShortWch>
                <v6Lto>2</v6Lto>
                <v6WtE>2</v6WtE>
                <v6Rtti>2</v6Rtti>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define> </Define>
                  <Undefine> </Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Cads>
              <Aads>
                <interw>2</interw>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <thumb>2</thumb>
                <SplitLS>2</SplitLS>
                <SwStkChk>2</SwStkChk>
                <NoWarn>2</NoWarn>
                <uSurpInc>2</uSurpInc>
                <useXO>2</useXO>
                <ClangAsOpt>0</ClangAsOpt>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Aads>
            </GroupArmAds>
          </GroupOption>
          <Files>
            <File>
              <FileName>fal_rtt.c</FileName>
              <FileType>1</FileType>
              <FilePath>packages\fal-v0.5.0\src\fal_rtt.c</FilePath>
            </File>
            <File>
              <FileName>fal.c</FileName>
              <FileType>1</FileType>
              <FilePath>packages\fal-v0.5.0\src\fal.c</FilePath>
            </File>
            <File>
              <FileName>fal_flash_sfud_port.c</FileName>
              <FileType>1</FileType>
              <FilePath>packages\fal-v0.5.0\samples\porting\fal_flash_sfud_port.c</FilePath>
            </File>
            <File>
              <FileName>fal_partition.c</FileName>
              <FileType>1</FileType>
              <FilePath>packages\fal-v0.5.0\src\fal_partition.c</FilePath>
            </File>
            <File>
              <FileName>fal_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>packages\fal-v0.5.0\src\fal_flash.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Filesystem</GroupName>
          <Files>
            <File>
              <FileName>dfs_posix.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\dfs\src\dfs_posix.c</FilePath>
            </File>
            <File>
              <FileName>dfs_fs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\dfs\src\dfs_fs.c</FilePath>
            </File>
            <File>
              <FileName>dfs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\dfs\src\dfs.c</FilePath>
            </File>
            <File>
              <FileName>dfs_file.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\dfs\src\dfs_file.c</FilePath>
            </File>
            <File>
              <FileName>devfs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\dfs\filesystems\devfs\devfs.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Finsh</GroupName>
          <Files>
            <File>
              <FileName>shell.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\finsh\shell.c</FilePath>
            </File>
            <File>
              <FileName>msh.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\finsh\msh.c</FilePath>
            </File>
            <File>
              <FileName>msh_file.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\finsh\msh_file.c</FilePath>
            </File>
            <File>
              <FileName>cmd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\finsh\cmd.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Kernel</GroupName>
          <Files>
            <File>
              <FileName>mem.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\mem.c</FilePath>
            </File>
            <File>
              <FileName>scheduler.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\scheduler.c</FilePath>
            </File>
            <File>
              <FileName>idle.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\idle.c</FilePath>
            </File>
            <File>
              <FileName>timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\timer.c</FilePath>
            </File>
            <File>
              <FileName>thread.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\thread.c</FilePath>
            </File>
            <File>
              <FileName>device.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\device.c</FilePath>
            </File>
            <File>
              <FileName>mempool.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\mempool.c</FilePath>
            </File>
            <File>
              <FileName>irq.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\irq.c</FilePath>
            </File>
            <File>
              <FileName>kservice.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\kservice.c</FilePath>
            </File>
            <File>
              <FileName>clock.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\clock.c</FilePath>
            </File>
            <File>
              <FileName>object.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\object.c</FilePath>
            </File>
            <File>
              <FileName>components.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\components.c</FilePath>
            </File>
            <File>
              <FileName>ipc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\src\ipc.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>libc</GroupName>
          <Files>
            <File>
              <FileName>time.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\libc\compilers\common\time.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Libraries</GroupName>
          <Files>
            <File>
              <FileName>stm32g4xx_hal_dma_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_dma_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_gpio.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_rcc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rcc_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_iwdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_iwdg.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_adc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_adc_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rtc.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_uart_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_uart_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_rtc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rtc_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_tim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_tim.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_cryp_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_cryp_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_pwr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_pwr.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_dac_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_dac_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_flash_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_flash_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_fdcan.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_fdcan.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_cryp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_cryp.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_usart_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_usart_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_dac.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_dac.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_rng.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rng.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_tim_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_tim_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_qspi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_qspi.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_wwdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_wwdg.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_usart.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_cortex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_cortex.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_rcc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rcc.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_dma.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_uart.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_flash.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_pwr_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_pwr_ex.c</FilePath>
            </File>
            <File>
              <FileName>system_stm32g4xx.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\CMSIS\Device\ST\STM32G4xx\Source\Templates\system_stm32g4xx.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_spi.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_adc.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_nor.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_nor.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_crc.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_hal_cordic.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_cordic.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_ll_cordic.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_ll_cordic.c</FilePath>
            </File>
            <File>
              <FileName>stm32g4xx_ll_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\libraries\STM32G4xx_HAL\STM32G4xx_HAL_Driver\Src\stm32g4xx_ll_gpio.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>littlefs</GroupName>
          <Files>
            <File>
              <FileName>dfs_lfs.c</FileName>
              <FileType>1</FileType>
              <FilePath>packages\littlefs-latest\dfs_lfs.c</FilePath>
            </File>
            <File>
              <FileName>lfs_util.c</FileName>
              <FileType>1</FileType>
              <FilePath>packages\littlefs-latest\lfs_util.c</FilePath>
            </File>
            <File>
              <FileName>lfs.c</FileName>
              <FileType>1</FileType>
              <FilePath>packages\littlefs-latest\lfs.c</FilePath>
            </File>
            <File>
              <FileName>lfs_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>packages\littlefs-latest\lfs_crc.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>SAL</GroupName>
          <GroupOption>
            <CommonProperty>
              <UseCPPCompiler>0</UseCPPCompiler>
              <RVCTCodeConst>0</RVCTCodeConst>
              <RVCTZI>0</RVCTZI>
              <RVCTOtherData>0</RVCTOtherData>
              <ModuleSelection>0</ModuleSelection>
              <IncludeInBuild>0</IncludeInBuild>
              <AlwaysBuild>0</AlwaysBuild>
              <GenerateAssemblyFile>2</GenerateAssemblyFile>
              <AssembleAssemblyFile>2</AssembleAssemblyFile>
              <PublicsOnly>2</PublicsOnly>
              <StopOnExitCode>11</StopOnExitCode>
              <CustomArgument></CustomArgument>
              <IncludeLibraryModules></IncludeLibraryModules>
              <ComprImg>1</ComprImg>
            </CommonProperty>
            <GroupArmAds>
              <Cads>
                <interw>2</interw>
                <Optim>0</Optim>
                <oTime>2</oTime>
                <SplitLS>2</SplitLS>
                <OneElfS>2</OneElfS>
                <Strict>2</Strict>
                <EnumInt>2</EnumInt>
                <PlainCh>2</PlainCh>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <wLevel>0</wLevel>
                <uThumb>2</uThumb>
                <uSurpInc>2</uSurpInc>
                <uC99>2</uC99>
                <uGnu>2</uGnu>
                <useXO>2</useXO>
                <v6Lang>0</v6Lang>
                <v6LangP>0</v6LangP>
                <vShortEn>2</vShortEn>
                <vShortWch>2</vShortWch>
                <v6Lto>2</v6Lto>
                <v6WtE>2</v6WtE>
                <v6Rtti>2</v6Rtti>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Cads>
              <Aads>
                <interw>2</interw>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <thumb>2</thumb>
                <SplitLS>2</SplitLS>
                <SwStkChk>2</SwStkChk>
                <NoWarn>2</NoWarn>
                <uSurpInc>2</uSurpInc>
                <useXO>2</useXO>
                <ClangAsOpt>0</ClangAsOpt>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Aads>
            </GroupArmAds>
          </GroupOption>
          <Files>
            <File>
              <FileName>netdev_ipaddr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\net\netdev\src\netdev_ipaddr.c</FilePath>
            </File>
            <File>
              <FileName>netdev.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\net\netdev\src\netdev.c</FilePath>
            </File>
            <File>
              <FileName>sal_socket.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\net\sal_socket\src\sal_socket.c</FilePath>
            </File>
            <File>
              <FileName>net_netdb.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\net\sal_socket\socket\net_netdb.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Utilities</GroupName>
          <Files>
            <File>
              <FileName>ulog.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\utilities\ulog\ulog.c</FilePath>
            </File>
            <File>
              <FileName>console_be.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\components\utilities\ulog\backend\console_be.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>FlashDB</GroupName>
          <Files>
            <File>
              <FileName>fdb_cmd.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\FlashDB-v1.1.0\src\fdb_cmd.c</FilePath>
            </File>
            <File>
              <FileName>fdb.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\FlashDB-v1.1.0\src\fdb.c</FilePath>
            </File>
            <File>
              <FileName>fdb_kvdb.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\FlashDB-v1.1.0\src\fdb_kvdb.c</FilePath>
            </File>
            <File>
              <FileName>fdb_tsdb.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\FlashDB-v1.1.0\src\fdb_tsdb.c</FilePath>
            </File>
            <File>
              <FileName>fdb_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\FlashDB-v1.1.0\src\fdb_utils.c</FilePath>
            </File>
            <File>
              <FileName>kvdb_basic_sample.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\FlashDB-v1.1.0\samples\kvdb_basic_sample.c</FilePath>
            </File>
            <File>
              <FileName>tsdb_sample.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\FlashDB-v1.1.0\samples\tsdb_sample.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>lid3dh</GroupName>
          <Files>
            <File>
              <FileName>lis3dh_reg.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\lis3dh\lis3dh_reg.c</FilePath>
            </File>
            <File>
              <FileName>port_sensor.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\lis3dh\port_sensor.c</FilePath>
            </File>
            <File>
              <FileName>sensor_st_lis3dh.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\packages\lis3dh\sensor_st_lis3dh.c</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components/>
    <files/>
  </RTE>

  <LayerInfo>
    <Layers>
      <Layer>
        <LayName>inverter</LayName>
        <LayPrjMark>1</LayPrjMark>
      </Layer>
    </Layers>
  </LayerInfo>

</Project>
