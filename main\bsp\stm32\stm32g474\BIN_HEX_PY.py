import os
import re
import shutil
import subprocess
import sys
from datetime import datetime
from xml.etree import ElementTree as ET
from typing import Dict
import argparse

# 强制刷新输出缓冲区
sys.stdout.reconfigure(line_buffering=True)
sys.stderr.reconfigure(line_buffering=True)

def get_project_root():
    """获取项目根目录，兼容打包后的可执行文件"""
    if getattr(sys, 'frozen', False):
        return os.path.dirname(sys.executable)
    else:
        return os.path.dirname(os.path.abspath(__file__))

def beautify_path(path):
    """美化路径显示，保留盘符，从项目根目录开始显示"""
    if not path:
        return path

    # 规范化路径
    normalized_path = os.path.normpath(os.path.abspath(path))

    # 分离盘符和路径部分
    drive, path_without_drive = os.path.splitdrive(normalized_path)


    # 获取当前脚本所在目录
    script_root = get_project_root()
    script_root_normalized = os.path.normpath(os.path.abspath(script_root))
    script_drive, script_path_without_drive = os.path.splitdrive(script_root_normalized)

    # 如果不在同一个盘符下，返回完整路径
    if drive.lower() != script_drive.lower():
        return normalized_path

    # 分割路径部分
    script_parts = script_path_without_drive.strip(os.sep).split(os.sep) if script_path_without_drive.strip(os.sep) else []
    target_parts = path_without_drive.strip(os.sep).split(os.sep) if path_without_drive.strip(os.sep) else []

    # 向上查找最多6级目录，寻找合适的项目根目录
    # 优先选择包含更多目标路径的根目录（更上级的目录）
    best_root_parts = None
    best_coverage = 0  # 记录最佳根目录能覆盖的目标路径层级数

    for levels_up in range(min(6, len(script_parts) + 1)):
        if levels_up == 0:
            # 当前脚本目录作为根目录
            root_parts = script_parts
        else:
            # 向上levels_up级目录
            root_parts = script_parts[:-levels_up] if levels_up < len(script_parts) else []

        if not root_parts:
            continue

        # 检查目标路径是否在这个根目录下
        if len(target_parts) >= len(root_parts):
            if target_parts[:len(root_parts)] == root_parts:
                # 计算这个根目录的"覆盖度"（能包含多少层目标路径）
                coverage = len(target_parts) - len(root_parts)

                # 选择覆盖度最高的根目录（即能包含最多目标路径层级的根目录）
                if coverage > best_coverage or best_root_parts is None:
                    best_root_parts = root_parts
                    best_coverage = coverage

    if best_root_parts:
        # 构建相对于项目根目录的路径
        # 展开显示最多5级目录层次，而不是只显示最后一级
        display_levels = min(5, len(best_root_parts))
        display_root_parts = best_root_parts[-display_levels:] if display_levels > 0 else best_root_parts
        display_root_path = os.sep.join(display_root_parts)

        relative_parts = target_parts[len(best_root_parts):]

        # 检查是否省略了中间路径（即显示的根目录不是紧接着盘符的）
        if len(best_root_parts) > display_levels:
            # 有中间路径被省略，显示省略号
            if relative_parts:
                relative_path = os.sep.join(relative_parts)
                return f"{drive}{os.sep}...{os.sep}{display_root_path}{os.sep}{relative_path}"
            else:
                return f"{drive}{os.sep}...{os.sep}{display_root_path}"
        else:
            # 显示的根目录就在盘符下，不需要省略号
            if relative_parts:
                relative_path = os.sep.join(relative_parts)
                return f"{drive}{os.sep}{display_root_path}{os.sep}{relative_path}"
            else:
                return f"{drive}{os.sep}{display_root_path}"

    # 如果找不到合适的根目录，显示盘符+最后几级目录
    if len(target_parts) > 3:
        simplified_parts = target_parts[-3:]
        return f"{drive}{os.sep}...{os.sep}{os.sep.join(simplified_parts)}"
    elif target_parts:
        return f"{drive}{os.sep}{os.sep.join(target_parts)}"

    return normalized_path

class ConfigManager:
    """配置管理器，用于读取XML配置文件"""
    
    def __init__(self, config_file=None):
        self.config_file = config_file or 'BIN_HEX_CONFIG.xml'
        self.config = self.load_config()
    
    def load_config(self) -> Dict:
        """加载XML配置文件"""
        config_path = os.path.join(get_project_root(), self.config_file)
        
        if not os.path.exists(config_path):
            print(f"错误: 配置文件不存在 - {beautify_path(config_path)}")
            print("请创建config.xml配置文件")
            raise FileNotFoundError(f"配置文件不存在: {beautify_path(config_path)}")
        
        try:
            tree = ET.parse(config_path)
            root = tree.getroot()
            
            config = {
                'paths': {
                    'bootloader_hex': root.find('.//bootloader_hex').text or '',
                    'main_hex': root.find('.//main_hex').text or '',
                    'main_bin': root.find('.//main_bin').text or '',
                    'output_dir': root.find('.//output_dir').text or '',
                    'startup_file': root.find('.//startup_file').text or '',
                    'keil_uvoptx': root.find('.//keil_uvoptx').text or '',
                    'keil_uvprojx': root.find('.//keil_uvprojx').text or ''
                },
                'settings': {
                    'stm32_model': root.find('.//stm32_model').text or '',
                    'project_name': root.find('.//project_name').text or '',
                    'version': root.find('.//version').text or '',
                    'output_prefix': root.find('.//output_prefix').text or 'burnprocess_py'
                },
                'project_releases': []
            }

            # 解析工程发布配置
            project_releases = root.find('.//project_releases')
            if project_releases is not None:
                for project in project_releases.findall('project'):
                    target_name_elem = project.find('target_name')
                    release_hex_elem = project.find('release_hex_name')
                    release_bin_elem = project.find('release_bin_name')

                    if target_name_elem is not None:
                        project_config = {
                            'target_name': target_name_elem.text or '',
                            'release_hex_name': release_hex_elem.text if release_hex_elem is not None else '',
                            'release_bin_name': release_bin_elem.text if release_bin_elem is not None else ''
                        }
                        config['project_releases'].append(project_config)
            
            print(f"成功加载配置文件: {beautify_path(config_path)}")
            return config
            
        except Exception as e:
            print(f"读取配置文件失败: {e}")
            raise
    
    def get_path(self, path_key: str) -> str:
        """获取配置的路径"""
        return self.config['paths'].get(path_key, '')
    
    def get_setting(self, setting_key: str) -> str:
        """获取配置的设置"""
        return self.config['settings'].get(setting_key, '')

    def get_project_release_config(self, target_name: str) -> Dict:
        """根据目标名称获取工程发布配置"""
        for project in self.config['project_releases']:
            if project['target_name'].lower() == target_name.lower():
                return project
        return {}

    def get_all_project_releases(self) -> list:
        """获取所有工程发布配置"""
        return self.config['project_releases']

def get_files_by_config(config_manager: ConfigManager):
    """根据配置文件获取文件路径"""
    project_root = get_project_root()
    
    # 获取配置的文件路径（只是目录路径）
    boot_hex_path = config_manager.get_path('bootloader_hex')
    main_hex_dir = config_manager.get_path('main_hex')
    main_bin_dir = config_manager.get_path('main_bin')
    
    # 转换为绝对路径
    if boot_hex_path and not os.path.isabs(boot_hex_path):
        boot_hex_path = os.path.join(project_root, boot_hex_path)
    
    if main_hex_dir and not os.path.isabs(main_hex_dir):
        main_hex_dir = os.path.join(project_root, main_hex_dir)
    
    if main_bin_dir and not os.path.isabs(main_bin_dir):
        main_bin_dir = os.path.join(project_root, main_bin_dir)
    
    # 从Keil工程文件获取输出名称
    output_name = get_keil_project_name(config_manager)
    if not output_name:
        print("错误: 无法从Keil工程文件获取输出名称")
        raise ValueError("无法获取Keil工程输出名称")
    
    # 构建完整的文件路径
    main_hex_path = os.path.join(main_hex_dir, f"{output_name}.hex")
    main_bin_path = os.path.join(main_bin_dir, f"{output_name}.bin")
    
    print(f"项目根目录  : {beautify_path(project_root)}")
    print(f"Boot HEX文件: {beautify_path(boot_hex_path)}")
    print(f"Main HEX目录: {beautify_path(main_hex_dir)}")
    print(f"Main BIN目录: {beautify_path(main_bin_dir)}")
    print(f"Keil输出名称: {output_name}")
    print(f"Main HEX文件: {beautify_path(main_hex_path)}")
    print(f"Main BIN文件: {beautify_path(main_bin_path)}")
    
    # 检查文件是否存在
    missing_files = []
    if not boot_hex_path or not os.path.exists(boot_hex_path):
        missing_files.append(f"Boot HEX文件: {beautify_path(boot_hex_path)}")

    if not os.path.exists(main_hex_path):
        missing_files.append(f"Main HEX文件: {beautify_path(main_hex_path)}")

    if not os.path.exists(main_bin_path):
        missing_files.append(f"Main BIN文件: {beautify_path(main_bin_path)}")
    
    if missing_files:
        print("\n错误: 以下文件不存在:")
        for file in missing_files:
            print(f"  - {file}")
        raise FileNotFoundError("配置的文件路径不存在")
    
    print("\n所有配置文件验证通过")
    return boot_hex_path, main_hex_path, main_bin_path

def extract_stm32_model_from_path(file_path: str) -> str:
    """从文件路径中提取STM32型号"""
    match = re.search(r'stm32([a-z0-9]+)', file_path, re.IGNORECASE)
    if match:
        return 'stm32' + match.group(1).lower()
    return "unknown"

def format_version(version_str: str) -> str:
    """格式化版本号"""
    if len(version_str) >= 5:
        major = version_str[0]
        minor = version_str[1:3]
        patch = version_str[3:5]
        return f"{major}.{minor}.{patch}"
    return version_str

def find_crc_exe(bin_path):
    """查找BIN_ADD_CRC.exe工具，优先在bin文件目录，然后在当前目录"""
    bin_dir = os.path.dirname(bin_path)
    current_dir = get_project_root()

    # 首先在bin文件目录查找
    crc_exe_in_bin_dir = os.path.join(bin_dir, "BIN_ADD_CRC.exe")
    if os.path.exists(crc_exe_in_bin_dir):
        print(f"找到CRC工具 : {beautify_path(crc_exe_in_bin_dir)}")
        return crc_exe_in_bin_dir, bin_dir

    # 然后在当前目录查找
    crc_exe_in_current_dir = os.path.join(current_dir, "BIN_ADD_CRC.exe")
    if os.path.exists(crc_exe_in_current_dir):
        print(f"找到CRC工具 : {beautify_path(crc_exe_in_current_dir)}")
        return crc_exe_in_current_dir, current_dir

    # 都找不到则报错
    raise FileNotFoundError(
        f"未找到BIN_ADD_CRC.exe工具。已搜索路径:\n"
        f"  1. {beautify_path(crc_exe_in_bin_dir)}\n"
        f"  2. {beautify_path(crc_exe_in_current_dir)}"
    )

def add_crc_to_bin(bin_path):
    """对bin文件添加CRC校验"""
    bin_dir = os.path.dirname(bin_path)

    # 查找CRC工具
    crc_exe, crc_work_dir = find_crc_exe(bin_path)

    print(f"正在添加CRC校验: {os.path.basename(bin_path)}")
    result = subprocess.run(
        [crc_exe, bin_path],
        capture_output=True,
        text=True,
        cwd=crc_work_dir
    )

    if result.returncode != 0:
        print(f"CRC工具输出: {result.stdout}")
        print(f"CRC工具错误: {result.stderr}")
        raise RuntimeError(f"CRC工具执行失败，返回代码: {result.returncode}")

    # 查找生成的CRC文件
    bin_name = os.path.splitext(os.path.basename(bin_path))[0]

    if bin_name.endswith('_crc'):
        crc_bin_name = f"{bin_name}.bin"
    else:
        crc_bin_name = f"{bin_name}_crc.bin"

    # CRC文件可能生成在工作目录或bin文件目录
    possible_crc_paths = [
        os.path.join(bin_dir, crc_bin_name),
        os.path.join(crc_work_dir, crc_bin_name)
    ]

    crc_bin_path = None
    for path in possible_crc_paths:
        if os.path.exists(path):
            crc_bin_path = path
            break

    if not crc_bin_path:
        #print(f"调试: bin目录内容 - {os.listdir(bin_dir)}")
        #print(f"调试: 工作目录内容 - {os.listdir(crc_work_dir)}")
        #print(f"调试: 期望文件名 - {crc_bin_name}")
        raise FileNotFoundError(f"未找到生成的CRC文件，已搜索: {possible_crc_paths}")

    print(f"找到生成CRC文件: {os.path.basename(crc_bin_path)}")
    return crc_bin_path

def parse_hex_lines(hex_lines):
    """解析hex行，返回 {绝对地址: 数据字节} 的字典，支持扩展线性地址"""
    addr_dict = {}
    ext_addr = 0
    for line in hex_lines:
        line = line.strip()
        if not line or not line.startswith(':'):
            continue
        byte_count = int(line[1:3], 16)
        addr = int(line[3:7], 16)
        rec_type = int(line[7:9], 16)
        data = line[9:9+byte_count*2]
        if rec_type == 0x04:  # 扩展线性地址
            ext_addr = int(data, 16) << 16
        elif rec_type == 0x00:  # 数据记录
            for i in range(byte_count):
                abs_addr = ext_addr + addr + i
                addr_dict[abs_addr] = data[i*2:i*2+2]
        # 其他类型不处理
    return addr_dict

def dict_to_hex_lines(addr_dict, line_len=16):
    """将地址字典转为hex行，自动处理扩展线性地址，返回字符串列表"""
    if not addr_dict:
        return []
    lines = []
    sorted_addrs = sorted(addr_dict.keys())
    cur_ext_addr = None
    i = 0
    while i < len(sorted_addrs):
        base_addr = sorted_addrs[i]
        ext_addr = base_addr >> 16
        if ext_addr != cur_ext_addr:
            # 写扩展线性地址记录
            rec = ':02000004{:04X}'.format(ext_addr)
            # 计算校验
            b = [2, 0, 0, 4, (ext_addr >> 8) & 0xFF, ext_addr & 0xFF]
            checksum = ((~(sum(b)) + 1) & 0xFF)
            rec += '{:02X}\n'.format(checksum)
            lines.append(rec)
            cur_ext_addr = ext_addr
        # 组一行数据
        line_addr = base_addr & 0xFFFF
        data_bytes = []
        for j in range(line_len):
            addr = (ext_addr << 16) + line_addr + j
            if i + j < len(sorted_addrs) and sorted_addrs[i + j] == addr:
                data_bytes.append(addr_dict[addr])
            else:
                break
        byte_count = len(data_bytes)
        if byte_count == 0:
            i += 1
            continue
        rec = ':{:02X}{:04X}00'.format(byte_count, line_addr)
        rec += ''.join(data_bytes)
        # 计算校验
        b = [byte_count, (line_addr >> 8) & 0xFF, line_addr & 0xFF, 0]
        b += [int(x, 16) for x in data_bytes]
        checksum = ((~(sum(b)) + 1) & 0xFF)
        rec += '{:02X}\n'.format(checksum)
        lines.append(rec)
        i += byte_count
    # 结束符
    lines.append(':00000001FF\n')
    return lines

def merge_hex_files(boot_hex_path, main_hex_path, output_hex_path):
    """合并两个hex文件，遇到重复地址以boot hex为准，自动处理扩展线性地址"""
    print(f"正在合并HEX文件: {os.path.basename(boot_hex_path)} 和 {os.path.basename(main_hex_path)}")
    with open(boot_hex_path, 'r') as f:
        boot_lines = f.readlines()
    with open(main_hex_path, 'r') as f:
        main_lines = f.readlines()
    boot_dict = parse_hex_lines(boot_lines)
    main_dict = parse_hex_lines(main_lines)
    # 合并，boot优先
    merged_dict = main_dict.copy()
    merged_dict.update(boot_dict)  # boot覆盖main
    merged_lines = dict_to_hex_lines(merged_dict)
    with open(output_hex_path, 'w') as f:
        f.writelines(merged_lines)
    print(f"合并后的HEX文件: {os.path.basename(output_hex_path)}")

def extract_version_from_startup_file(config_manager: ConfigManager):
    """从配置的启动文件中提取版本号"""
    startup_file_path = config_manager.get_path('startup_file')
    
    if not startup_file_path:
        print("警告: 未配置启动文件路径")
        return None
    
    # 转换为绝对路径
    root_dir = get_project_root()
    if not os.path.isabs(startup_file_path):
        startup_file_path = os.path.join(root_dir, startup_file_path)
    
    if not os.path.exists(startup_file_path):
        print(f"警告: 启动文件不存在 - {beautify_path(startup_file_path)}")
        return None

    print(f"使用配置的启动文件: {beautify_path(startup_file_path)}")

    # 从启动文件中提取版本号
    version_patterns = [
        r'FIREWARE_VER\s+EQU\s+(\d+)',
        r'FIRMWARE_VER\s+EQU\s+(\d+)',
        r'FW_VERSION\s+EQU\s+(\d+)',
        r'VERSION\s+EQU\s+(\d+)',
        r'APP_VERSION\s+EQU\s+(\d+)',
    ]

    try:
        with open(startup_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
            for pattern in version_patterns:
                match = re.search(pattern, content)
                if match:
                    version_str = match.group(1)
                    print(f"从启动文件中提取到版本号: {version_str}")
                    return version_str

            print("警告: 未在启动文件中找到版本号定义")
            return None
            
    except Exception as e:
        print(f"读取启动文件失败: {e}")
        return None

def process_files(boot_hex_path, main_hex_path, main_bin_path, config_manager: ConfigManager):
    """处理配置的文件"""
    print(f"Boot HEX文件: {beautify_path(boot_hex_path)}")
    print(f"Main HEX文件: {beautify_path(main_hex_path)}")
    print(f"Main BIN文件: {beautify_path(main_bin_path)}")

    # 检查CRC工具（使用新的查找函数进行预检查）
    try:
        find_crc_exe(main_bin_path)
        print("CRC工具检查通过")
    except FileNotFoundError as e:
        raise FileNotFoundError(f"CRC工具检查失败: {e}")

    # 获取STM32型号
    stm32_model = config_manager.get_setting('stm32_model')
    if not stm32_model:
        stm32_model = extract_stm32_model_from_path(main_bin_path)
    print(f"STM32型号: {stm32_model}")

    # 获取版本号
    version_str = config_manager.get_setting('version')
    if not version_str:
        version_str = extract_version_from_startup_file(config_manager)
    
    if not version_str:
        print("警告: 未能从启动文件中提取版本号")
        version_str = input("请输入版本号(例如10014代表1.00.14): ").strip()

    if not version_str:
        print("错误: 版本号不能为空")
        return

    # 从Keil工程文件获取项目名称
    project_name = get_keil_project_name(config_manager)
    if not project_name:
        # 如果从Keil工程文件获取失败，尝试从XML配置获取
        project_name = config_manager.get_setting('project_name')
        if not project_name:
            print("错误: 无法从Keil工程文件或config.xml中获取项目名称")
            print("请确保:")
            print("1. 在config.xml中正确配置keil_uvoptx和keil_uvprojx路径")
            print("2. Keil工程文件存在且格式正确")
            print("3. 或在config.xml中配置project_name作为备选")
            return

    print(f"项目名称       : {project_name}")

    # 格式化版本号
    version_formatted = format_version(version_str)
    version_suffix = f"V{version_formatted}"

    # 创建输出目录
    date_suffix = datetime.now().strftime("%Y%m%d")
    output_prefix = config_manager.get_setting('output_prefix')
    
    # 获取配置的输出目录
    output_base_dir = config_manager.get_path('output_dir')
    if not output_base_dir:
        output_base_dir = os.getcwd()  # 默认使用当前目录
    
    # 转换为绝对路径
    project_root = get_project_root()
    if not os.path.isabs(output_base_dir):
        output_base_dir = os.path.join(project_root, output_base_dir)
    
    output_dir = os.path.join(output_base_dir, f'{output_prefix}_{date_suffix}')
    os.makedirs(output_dir, exist_ok=True)
    print(f"输出目录       : {beautify_path(output_dir)}")

    # 添加CRC校验
    crc_bin_path = add_crc_to_bin(main_bin_path)

    # 合并hex文件（增加日期后缀）
    merged_hex_name = f"{project_name}_{date_suffix}_{version_suffix}.hex"
    merged_hex_path = os.path.join(output_dir, merged_hex_name)
    merge_hex_files(boot_hex_path, main_hex_path, merged_hex_path)

    # 复制CRC bin文件并重命名（增加日期后缀）
    crc_bin_name = f"{project_name}_{date_suffix}_{version_suffix}.bin"
    crc_bin_output_path = os.path.join(output_dir, crc_bin_name)
    shutil.copy2(crc_bin_path, crc_bin_output_path)

    # 获取当前工程的目标名称
    current_target_name = get_keil_current_target_name(config_manager)
    if not current_target_name:
        print("警告: 无法获取当前工程的目标名称，跳过发布文件生成")
        current_target_name = ""

    # 根据当前目标名称获取发布配置
    release_config = config_manager.get_project_release_config(current_target_name)

    # 创建发布文件（复制原文件）
    release_hex_path = None
    release_bin_path = None

    if release_config and release_config.get('release_hex_name'):
        release_hex_name = release_config['release_hex_name']
        # 格式化版本号为 V2-00 格式（只取前两位，用短横线分隔）
        if len(version_str) >= 2:
            major = version_str[0]
            minor = version_str[1:3]
            release_version = f"V{major}-{minor}"
        else:
            release_version = f"V{version_str}"
        
        release_hex_filename = f"{release_hex_name}_{release_version}_BSM.hex"
        release_hex_path = os.path.join(output_dir, release_hex_filename)
        shutil.copy2(merged_hex_path, release_hex_path)
        print(f"创建发布HEX文件: {release_hex_filename} (目标工程: {current_target_name})")

    if release_config and release_config.get('release_bin_name'):
        release_bin_name = release_config['release_bin_name']
        # 格式化版本号为 V2-00 格式（只取前两位，用短横线分隔）
        if len(version_str) >= 2:
            major = version_str[0]
            minor = version_str[1:3]
            release_version = f"V{major}-{minor}"
        else:
            release_version = f"V{version_str}"
            
        release_bin_filename = f"{release_bin_name}_{release_version}_BSM.bin"
        release_bin_path = os.path.join(output_dir, release_bin_filename)
        shutil.copy2(crc_bin_output_path, release_bin_path)
        print(f"创建发布BIN文件: {release_bin_filename} (目标工程: {current_target_name})")

    if not release_config:
        print(f"信息: 当前目标工程 '{current_target_name}' 未配置发布文件名，跳过发布文件生成")
        print("可用的工程发布配置:")
        all_releases = config_manager.get_all_project_releases()
        if all_releases:
            for proj in all_releases:
                print(f"  - 目标名称: {proj['target_name']}")
        else:
            print("  - 无工程发布配置")

    print(f"\n处理完成！文件已输出到:")
    print(f"  1. {beautify_path(crc_bin_output_path)} ({os.path.getsize(crc_bin_output_path)} 字节)")
    print(f"  2. {beautify_path(merged_hex_path)} ({os.path.getsize(merged_hex_path)} 字节)")

    file_count = 3
    if release_hex_path and os.path.exists(release_hex_path):
        print(f"  {file_count}. {beautify_path(release_hex_path)} ({os.path.getsize(release_hex_path)} 字节)")
        file_count += 1
    if release_bin_path and os.path.exists(release_bin_path):
        print(f"  {file_count}. {beautify_path(release_bin_path)} ({os.path.getsize(release_bin_path)} 字节)")

def get_keil_current_target_name(config_manager: ConfigManager):
    """从配置的Keil工程文件中获取当前激活的目标名称"""
    project_root = get_project_root()

    # 获取配置的Keil工程文件路径
    uvoptx_path = config_manager.get_path('keil_uvoptx')

    if not uvoptx_path:
        print("警告: 未配置keil_uvoptx路径")
        return None

    # 转换为绝对路径
    if not os.path.isabs(uvoptx_path):
        uvoptx_path = os.path.join(project_root, uvoptx_path)

    # 检查文件是否存在
    if not os.path.exists(uvoptx_path):
        print(f"警告: .uvoptx文件不存在 - {beautify_path(uvoptx_path)}")
        return None

    print(f"读取当前目标名称: {beautify_path(uvoptx_path)}")

    # 使用正则表达式直接从XML文件中提取当前目标名称
    current_target_name = None
    try:
        with open(uvoptx_path, 'r', encoding='utf-8') as f:
            content = f.read()

            # 查找所有Target块
            import re
            target_pattern = r'<Target>(.*?)</Target>'
            targets = re.findall(target_pattern, content, re.DOTALL)

            for i, target_content in enumerate(targets):
                # 提取TargetName
                name_match = re.search(r'<TargetName>([^<]*)</TargetName>', target_content)
                # 提取IsCurrentTarget
                current_match = re.search(r'<IsCurrentTarget>([^<]*)</IsCurrentTarget>', target_content)

                target_name = name_match.group(1) if name_match else "未知"
                is_current = current_match.group(1) if current_match else "未设置"

                if current_match and current_match.group(1).strip() == '1':
                    current_target_name = target_name.strip()
                    print(f"找到当前目标名称: {current_target_name}")
                    break

    except Exception as e:
        print(f"读取{beautify_path(uvoptx_path)}失败: {e}")
        return None

    if not current_target_name:
        print("警告: 未找到当前激活的目标")
        return None

    return current_target_name

def get_keil_project_name(config_manager: ConfigManager):
    """从配置的Keil工程文件中获取项目名称"""
    project_root = get_project_root()
    
    # 获取配置的Keil工程文件路径
    uvoptx_path = config_manager.get_path('keil_uvoptx')
    uvprojx_path = config_manager.get_path('keil_uvprojx')
    
    if not uvoptx_path:
        print("警告: 未配置keil_uvoptx路径")
        return None
    
    if not uvprojx_path:
        print("警告: 未配置keil_uvprojx路径")
        return None
    
    # 转换为绝对路径
    if not os.path.isabs(uvoptx_path):
        uvoptx_path = os.path.join(project_root, uvoptx_path)
    
    if not os.path.isabs(uvprojx_path):
        uvprojx_path = os.path.join(project_root, uvprojx_path)
    
    # 检查文件是否存在
    if not os.path.exists(uvoptx_path):
        print(f"警告: .uvoptx文件不存在 - {beautify_path(uvoptx_path)}")
        return None

    if not os.path.exists(uvprojx_path):
        print(f"警告: .uvprojx文件不存在 - {beautify_path(uvprojx_path)}")
        return None

    print(f"使用Keil工程文件:")
    print(f"  - {beautify_path(uvoptx_path)}")
    print(f"  - {beautify_path(uvprojx_path)}")
    
    # 使用正则表达式直接从XML文件中提取当前目标名称
    current_target_name = None
    try:
        with open(uvoptx_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 查找所有Target块
            import re
            target_pattern = r'<Target>(.*?)</Target>'
            targets = re.findall(target_pattern, content, re.DOTALL)
            
            #print(f"调试: 通过正则表达式找到 {len(targets)} 个Target")
            
            
            for i, target_content in enumerate(targets):
                # 提取TargetName
                name_match = re.search(r'<TargetName>([^<]*)</TargetName>', target_content)
                # 提取IsCurrentTarget
                current_match = re.search(r'<IsCurrentTarget>([^<]*)</IsCurrentTarget>', target_content)
                
                target_name = name_match.group(1) if name_match else "未知"
                is_current = current_match.group(1) if current_match else "未设置"
                
                #print(f"调试: Target {i+1} - 名称: {target_name}, 是否当前: '{is_current}'")
                
                if current_match and current_match.group(1).strip() == '1':
                    current_target_name = target_name.strip()
                    print(f"找到目标工程   : {current_target_name}")
                    break
            
    except Exception as e:
        print(f"读取{beautify_path(uvoptx_path)}失败: {e}")
        return None
    
    if not current_target_name:
        print("警告: 未找到当前激活的目标")
        return None
    
    # 从.uvprojx文件中找到对应目标的OutputName
    try:
        with open(uvprojx_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # 查找所有Target块
            target_pattern = r'<Target>(.*?)</Target>'
            targets = re.findall(target_pattern, content, re.DOTALL)
            
            #print(f"调试: 在.uvprojx中通过正则表达式找到 {len(targets)} 个Target")
            
            for i, target_content in enumerate(targets):
                # 提取TargetName
                name_match = re.search(r'<TargetName>([^<]*)</TargetName>', target_content)
                target_name = name_match.group(1).strip() if name_match else "未知"
                
                #print(f"调试: .uvprojx Target {i+1} - 名称: {target_name}")
                
                if target_name == current_target_name:
                    # 查找OutputName
                    output_match = re.search(r'<OutputName>([^<]*)</OutputName>', target_content)
                    if output_match:
                        output_name = output_match.group(1).strip()
                        print(f"找到输出名称   : {output_name}")
                        return output_name
                    else:
                        print(f"调试: 在目标 {current_target_name} 中未找到OutputName")
        
    except Exception as e:
        print(f"读取{beautify_path(uvprojx_path)}失败: {e}")
        return None
    
    print("警告: 未找到匹配目标的输出名称")
    return None

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(
        description='基于XML配置的固件处理工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog='''
使用示例:
  python BIN_HEX_PY.py                           # 使用默认配置文件 BIN_HEX_CONFIG.xml
  python BIN_HEX_PY.py -c config_pfc.xml         # 使用指定配置文件
  python BIN_HEX_PY.py --config inverter.xml     # 使用指定配置文件
        '''
    )
    
    parser.add_argument(
        '-c', '--config',
        default='BIN_HEX_CONFIG.xml',
        help='指定配置文件名 (默认: BIN_HEX_CONFIG.xml)'
    )
    
    args = parser.parse_args()
    
    print("=== 基于XML配置的固件处理工具 ===")
    print(f"使用配置文件: {args.config}")
    
    try:
        # 加载配置
        config_manager = ConfigManager(args.config)
        
        # 获取配置的文件路径
        boot_hex_path, main_hex_path, main_bin_path = get_files_by_config(config_manager)
        
        # 处理文件
        process_files(boot_hex_path, main_hex_path, main_bin_path, config_manager)
        
    except FileNotFoundError as e:
        print(f"配置错误: {e}")
        return
    except Exception as e:
        print(f"处理失败: {e}")
        return

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"错误: {str(e)}")
        exit(1)
