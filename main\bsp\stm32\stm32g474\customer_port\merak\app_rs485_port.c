/******************** (C) COPYRIGHT 2025     ***********************************
* File Name          : app_rs485_port.c
* Author             : xiou
* Version            : V1.0
* Date               :
* Description        : PMSM1000平台控制器RS485调速通信协议V1.15
********************************************************************************/
#include <rtthread.h>
#include <rtdevice.h>
#include "uapp.h"
#include <board.h>
#define DBG_SECTION_NAME               "modbus"
#define DBG_LEVEL                      DBG_LOG
#include <rtdbg.h>
#include "drv_rs485.h"
#include "alg_modbus_common.h"

#define SLAVE_ADDR       (vfd.serial_addr)
#define MY_MODBUS_FUNC_0x41 (0x41)
#define MY_MODBUS_FUNC_0x01 (0x01)


extern void rs485_send_frame(const void *buff, int size);
extern uint32_t  HardfaultNormalDelay;
static uint8_t rx_buf[512] = {0};
static uint8_t tx_buf[512] = {0};

uint16_t reg_read[32];
uint16_t reg_faultIO;
uint16_t reg_write[4] = {0x0101, 0, 0};

typedef struct
{
    uint32_t  rxcnt;
    uint32_t  txcnt;
    uint16_t  reg;

    uint8_t   year;
    uint8_t   month;
    uint8_t   day;
    uint8_t   hour;
    uint8_t   min;
    uint8_t   sec;

    uint8_t   year_month_flag   : 1;
    uint8_t   day_hour_flag     : 1;
    uint8_t   min_sec_flag      : 1;

    uint8_t   recv_addr;
} custm_com_t;

custm_com_t modbus_slave = {0};

uint8_t mdbus_get_addr(void)
{
    if (modbus_slave.recv_addr != 0)
        return modbus_slave.recv_addr;
    else
        return SLAVE_ADDR;
}

extern UART_HandleTypeDef huart2;


int Rs485_Baudrate_Get(void)
{
    if ((nvs_datas.config.rs485_baudrate >= 9600) && (nvs_datas.config.rs485_baudrate <= 921600))
        return nvs_datas.config.rs485_baudrate;
    else
    {
        nvs_datas.config.rs485_baudrate  = 19200;
        nvs_datas.config.rs485_check_bit = 0;
        return nvs_datas.config.rs485_baudrate;
    }
}

uint16_t *mdbus_regaddr_maptoarray(uint16_t reg_addr, uint16_t funcode)
{
    uint16_t *preg_base = RT_NULL;

    if (((funcode == 0x10) || (funcode == 0x06)) && (reg_addr < 4))      preg_base = reg_write;
    else if (((funcode == 0x41) || (funcode == 0x03)) && (reg_addr < 32))    preg_base = reg_read;
    else if ((funcode == 0x01))                      preg_base = reg_faultIO;

    return preg_base;
}

uint16_t *mdbus_regaddr_maptoreg(uint16_t reg_addr, uint8_t funcode)
{
    uint16_t *preg_base = RT_NULL;
    uint16_t preg_offset = RT_NULL;

    preg_offset = reg_addr & 0xFF;

    preg_base = mdbus_regaddr_maptoarray(reg_addr, funcode);

    if ((preg_base == reg_write) && (preg_offset < 4))
        return &preg_base[preg_offset];
    else if ((preg_base == reg_read) && (preg_offset < 32))
        return &preg_base[preg_offset];
    else
        return RT_NULL;
}

/**
  * @brief USART2 Initialization Function
  * @param None
  * @retval None
  */
int MX_USART2_UART_Init(uint32_t BaudRate)
{
    /* DMA controller clock enable */
    __HAL_RCC_DMAMUX1_CLK_ENABLE();
    __HAL_RCC_DMA1_CLK_ENABLE();

    /* DMA interrupt init */
    HAL_NVIC_SetPriority(DMA1_Channel1_IRQn, 3, 3);
    HAL_NVIC_EnableIRQ(DMA1_Channel1_IRQn);

    HAL_NVIC_SetPriority(DMA1_Channel7_IRQn, 3, 3);
    HAL_NVIC_EnableIRQ(DMA1_Channel7_IRQn);

    /* USART1_IRQn interrupt configuration */
    HAL_NVIC_SetPriority(USART2_IRQn, 3, 1);
    HAL_NVIC_EnableIRQ(USART2_IRQn);

    /* USER CODE END USART2_Init 1 */
    huart2.Instance = USART2;

    if (vfd.usart2_default_flag) /**!> PTU Type */
    {
        huart2.Init.BaudRate = BaudRate;
        huart2.Init.WordLength = UART_WORDLENGTH_8B;
        huart2.Init.StopBits = UART_STOPBITS_1;
        huart2.Init.Parity = UART_PARITY_NONE;
    }
    else
    {
        huart2.Init.BaudRate = BaudRate;
        huart2.Init.StopBits = UART_STOPBITS_1;

        if (nvs_datas.config.rs485_check_bit == 1)
        {
            huart2.Init.Parity = UART_PARITY_ODD;
            huart2.Init.WordLength = UART_WORDLENGTH_9B;
        }
        else if (nvs_datas.config.rs485_check_bit == 2)
        {
            huart2.Init.Parity = UART_PARITY_EVEN;
            huart2.Init.WordLength = UART_WORDLENGTH_9B;
        }
        else
        {
            huart2.Init.Parity = UART_PARITY_NONE;
            huart2.Init.WordLength = UART_WORDLENGTH_8B;
        }
    }

    huart2.Init.Mode = UART_MODE_TX_RX;
    huart2.Init.HwFlowCtl = UART_HWCONTROL_NONE;
    huart2.Init.OverSampling = UART_OVERSAMPLING_16;
    huart2.Init.OneBitSampling = UART_ONE_BIT_SAMPLE_DISABLE;
    huart2.Init.ClockPrescaler = UART_PRESCALER_DIV1;
    huart2.AdvancedInit.AdvFeatureInit = UART_ADVFEATURE_NO_INIT;

    if (HAL_UART_Init(&huart2) != HAL_OK)
    {
        Error_Handler();
    }
    if (HAL_UARTEx_SetTxFifoThreshold(&huart2, UART_TXFIFO_THRESHOLD_1_8) != HAL_OK)
    {
        Error_Handler();
    }
    if (HAL_UARTEx_SetRxFifoThreshold(&huart2, UART_RXFIFO_THRESHOLD_1_8) != HAL_OK)
    {
        Error_Handler();
    }
    if (HAL_UARTEx_DisableFifoMode(&huart2) != HAL_OK)
    {
        Error_Handler();
    }

    __HAL_DMA_DISABLE_IT(huart2.hdmatx, DMA_IT_HT);
    __HAL_DMA_DISABLE_IT(huart2.hdmarx, DMA_IT_HT);

    HAL_UART_Receive_DMA(&huart2, RxBuffer_UR_DMA, UR_RX_BUFFER_SIZE_MAX);
    R485_DE_RX();
    /* USER CODE BEGIN USART2_Init 2 */
    return 0;
}

#include "app_ad.h"
int modbus_data_update(void)
{
    static uint8_t first = 1;

    uint16_t fault_code = 0;

    U32_SET_BIT(fault_code, 0, 0); /*-*/
    U32_SET_BIT(fault_code, 1, vfd.diag.dc_11 | vfd.diag.dc_69 | vfd.diag.dc_13 | vfd.diag.dc_14); /*过压保护*/
    U32_SET_BIT(fault_code, 2, vfd.diag.dc_12 | vfd.diag.dc_70 | vfd.diag.dc_15);/*欠压保护*/
    U32_SET_BIT(fault_code, 3, vfd.diag.dc_82 | vfd.diag.dc_83 | vfd.diag.dc_84 | vfd.diag.dc_85);/*过温保护*/
    U32_SET_BIT(fault_code, 4, 0);/*-*/
    U32_SET_BIT(fault_code, 5, (vfd.bit.diag_lvl_1||vfd.bit.diag_lvl_2)); /*停机类故障归为:启动异常*/
    U32_SET_BIT(fault_code, 6, vfd.diag.dc_74 | vfd.diag.dc_08);/*刹车保护*/
    U32_SET_BIT(fault_code, 7, 0); /*堵转保护*/
    U32_SET_BIT(fault_code, 8, vfd.diag.dc_25 | vfd.diag.dc_26 | vfd.diag.dc_27); /*输出缺相*/
    reg_faultIO = fault_code;

    uint16_t bit_data = 0;
    U32_SET_BIT(bit_data, 0, vfd.ctrl.start); /*-*/
    U32_SET_BIT(bit_data, 3, VFD_INPUT_AC); /*-*/
    U32_SET_BIT(bit_data, 7, vfd.bit.kmon_actived); /*-*/

    reg_read[0x00] = bit_data;

    if (vfd.ctrl.sys_st == ST_ERROR_LOCK)
        reg_read[0x01] = 0x04;//start error
    else if (vfd.ctrl.sys_st == ST_ERROR)
        reg_read[0x01] = 0x03;//error
    else if (mcsdk.ControlState == ST_START)
        reg_read[0x01] = 0x01;//start
    else if (mcsdk.ControlState == ST_RUN)
        reg_read[0x01] = 0x02;//run
    else if ((mcsdk.ControlState == ST_STOP) || (mcsdk.ControlState == ST_IDLE))
        reg_read[0x01] = 0x05;//stop
    else
        reg_read[0x01] = 0x00;//IDLE
    // 0x4 启动失败

    reg_read[0x02] = fault_code;
    reg_read[0x03] = 0;

    uint16_t  reg_L = (reg_write[0] & 0xFF);
    uint16_t  reg_H = 0;
    if(VFD_INPUT_NULL||VFD_INPUT_UNDEF) reg_H = 0;
    else if(VFD_INPUT_AC) reg_H = 3;
    else if(VFD_INPUT_DC) reg_H = 2;
    
    reg_read[0x04] = reg_L | (reg_H<<8);
    
    reg_read[0x05] = mcsdk.MotorSpeed;
    reg_read[0x06] = vfd.ad.temp_igbt / 10;
    reg_read[0x07] = mcsdk.BusVoltage;
    reg_read[0x08] = vfd.ad.ac_iout_u * 10;
    reg_read[0x09] = vfd.ad.ac_iout_v * 10;
    reg_read[0x0A] = vfd.ad.ac_iout_w * 10;

    reg_read[0x0B] = vfd.spi3_lis3dh.filter_xyz.data.acce.x;
    reg_read[0x0C] = vfd.spi3_lis3dh.filter_xyz.data.acce.y;
    reg_read[0x0D] = vfd.spi3_lis3dh.filter_xyz.data.acce.z;
    reg_read[0x0E] = vfd.spi3_lis3dh.vector;

    uint32_t run_times = rt_tick_get() / RT_TICK_PER_SECOND;
    reg_read[0x0F] = (run_times >> 16) & 0xFFFF;
    reg_read[0x10] = (run_times >> 0)  & 0xFFFF;

    reg_read[0x11] = VFD_SOFT_MAIN;
    reg_read[0x12] = (((VFD_SOFT_SUB / 10) << 8) & 0xFF00) | ((VFD_SOFT_SUB % 10) << 0) & 0x00FF;
    
    reg_read[0x13] = vfd.ad.temp_cap / 10;
    reg_read[0x14] = vfd.ad.temp_HDC1080 / 10;
    reg_read[0x15] = vfd.ad.temp_inv_dcl / 10;
    reg_read[0x16] = vfd.ad.moisture_HDC1080 / 10;
    
    reg_read[0x19] = VFD_SOFT_MAIN;
    reg_read[0x1A] = (((VFD_SOFT_SUB / 10) << 8) & 0xFF00) | ((VFD_SOFT_SUB % 10) << 0) & 0x00FF;

    if (!com_modbus.flag_normal) return -1;

    if (mcsdk.MB_ResetLock == 0x5A)
    {
        mcsdk.MB_ResetLock = 0;
        diag_lockstop_reset();
    }

    return 0;
}

void modbus_update_rtc(void)
{
    if (modbus_slave.year_month_flag
            && modbus_slave.day_hour_flag
            && modbus_slave.min_sec_flag)
    {
        modbus_slave.year_month_flag = 0;
        modbus_slave.day_hour_flag = 0;
        modbus_slave.min_sec_flag = 0;

        // uint8_t year = (reg_0x2000[3] >> 8) & 0xFF;
        // uint8_t mon  = (reg_0x2000[3] >> 0) & 0xFF;
        // uint8_t day  = (reg_0x2000[4] >> 8) & 0xFF;
        // uint8_t hour = (reg_0x2000[4] >> 0) & 0xFF;
        // uint8_t min  = (reg_0x2000[5] >> 8) & 0xFF;
        // uint8_t sec  = (reg_0x2000[5] >> 0) & 0xFF;

        // timedate_sync_rtc(year,mon,day,hour,min,sec,5);
    }
}


int my_modbus_write_reg(uint16_t reg, uint16_t data)
{
    char str[8] = {0};

    switch (reg)
    {
    case 0x0000:
        mcsdk.MB_SourceMode = (data >> 8) & 0xFF;
        mcsdk.MB_RunMode    = (data >> 0) & 0xFF;
        if (mcsdk.MB_RunMode == 0x1) /*转速模式*/
        {
            mcsdk.CtrlMode = CTRL_MODE_RPM;
            mcsdk.MB_Speed_Setup = (abs(mcsdk.MB_Speed_Setup) < 50) ?  0 : mcsdk.MB_Speed_Setup;
            vfd.ctrl.set_freq = (mcsdk.MB_Speed_Setup) * funcCode.code.motorParaM1.elem.ratingFrq / funcCode.code.motorParaM1.elem.ratingSpeed;
        }
        else if (mcsdk.MB_RunMode == 0x2) /*风量调速模式*/
        {
            mcsdk.CtrlMode = CTRL_MODE_FAN_LVL;
            vfd.ctrl.set_freq = mcsdk.MB_Fan_LVL * 15000 * 0.33333f;
        }
        else if (mcsdk.MB_RunMode == 0x3) /*0-10V调速模式*/
        {
            mcsdk.CtrlMode = CTRL_MODE_AUX1;
        }
        else if (mcsdk.MB_RunMode == 0x0) /*停机模式*/
        {
            mcsdk.CtrlMode = CTRL_MODE_STOP;
            vfd.ctrl.set_freq = 0;
        }
        break;
    case 0x0001:
        mcsdk.MB_Fan_LVL   = data;
        if (mcsdk.MB_RunMode == 0x2) /*风量调速模式*/
        {
            mcsdk.CtrlMode = CTRL_MODE_FAN_LVL;
            vfd.ctrl.set_freq = mcsdk.MB_Fan_LVL * funcCode.code.motorParaM1.elem.ratingFrq * 0.33333f;
        }
        break;
    case 0x0002:
        mcsdk.MB_Speed_Setup = data;
        if (mcsdk.MB_RunMode == 0x1) /*转速模式*/
        {
            mcsdk.MB_Speed_Setup = (mcsdk.MB_Speed_Setup > 2000) ? 2000:mcsdk.MB_Speed_Setup;
            mcsdk.MB_Speed_Setup = (abs(mcsdk.MB_Speed_Setup) < 50) ?  0 : mcsdk.MB_Speed_Setup;
            vfd.ctrl.set_freq = (mcsdk.MB_Speed_Setup + 1) * funcCode.code.motorParaM1.elem.ratingFrq / funcCode.code.motorParaM1.elem.ratingSpeed;
        }
        break;
    case 0x0003:
        if (nvs_datas.config.serial_com_id != data)
        {
            if (((data >= 0x21) && (data <= 0x28))
                || (data == 0))
            {
                nvs_datas.config.serial_com_id = data;
                nvsdata_write_config();
            }
        }
        break;
    default:
        break;

    }
}


int my_modbus_unpack(const uint8_t *buff, uint16_t size)
{
    uint8_t  err_flag = 0;
    uint16_t *preg_base = NULL;
    uint16_t reg_addr = 0;
    uint16_t reg_nums = 0;
    uint16_t reg_data = 0;
    uint8_t  reg_bytes = 0;
    uint16_t crc = 0;
    uint8_t  offset = 0;
    uint8_t  unpack_ok = 0;

    if (size < 512)
        rt_memcpy(rx_buf, buff, size);
    else
        return 0;

    if ((SLAVE_ADDR == rx_buf[0]) || (VFD_SERIAL_DEFAULT_ID == rx_buf[0]))
    {
        modbus_slave.recv_addr = rx_buf[0];
        //unpack
        reg_addr = *(uint16_t *)&rx_buf[2];
        reg_addr = mb_invert_u16(reg_addr);
        reg_nums = mb_invert_u16(*(uint16_t *)&rx_buf[4]);
        offset   = reg_addr & 0xFF;
        preg_base = mdbus_regaddr_maptoreg(reg_addr, rx_buf[1]);

        switch (rx_buf[1])
        {
        case MY_MODBUS_FUNC_0x41:
            if (rx_buf[4] >= 6)
            {
                reg_write[0] = mb_invert_u16(*(uint16_t *)&rx_buf[5]);
                reg_write[1] = mb_invert_u16(*(uint16_t *)&rx_buf[7]);
                reg_write[2] = mb_invert_u16(*(uint16_t *)&rx_buf[9]);

                mcsdk.MB_SourceMode = (reg_write[0] >> 8) & 0xFF;
                mcsdk.MB_RunMode    = (reg_write[0] >> 0) & 0xFF;
                mcsdk.MB_ResetLock   = reg_write[1];
                mcsdk.MB_Speed_Setup = reg_write[2];

                if (mcsdk.MB_RunMode == 0x1) /*转速模式*/
                {
                    mcsdk.CtrlMode = CTRL_MODE_RPM;
                    mcsdk.MB_Speed_Setup = (mcsdk.MB_Speed_Setup > 2000) ? 2000:mcsdk.MB_Speed_Setup;
                    mcsdk.MB_Speed_Setup = (abs(mcsdk.MB_Speed_Setup) < 50) ?  0 : mcsdk.MB_Speed_Setup;
                    vfd.ctrl.set_freq = (mcsdk.MB_Speed_Setup) * funcCode.code.motorParaM1.elem.ratingFrq / funcCode.code.motorParaM1.elem.ratingSpeed;
                }
                else if (mcsdk.MB_RunMode == 0x2) /*风量调速模式*/
                {
                    mcsdk.CtrlMode = CTRL_MODE_FAN_LVL;
                    vfd.ctrl.set_freq = mcsdk.MB_Fan_LVL * funcCode.code.motorParaM1.elem.ratingFrq * 0.33333f;
                }
                else if (mcsdk.MB_RunMode == 0x3) /*0-10V调速模式*/
                {
                    mcsdk.CtrlMode = CTRL_MODE_AUX1;
                }
                else if (mcsdk.MB_RunMode == 0x0) /*停机模式*/
                {
                    mcsdk.CtrlMode = CTRL_MODE_STOP;
                    vfd.ctrl.set_freq = 0;
                }

                preg_base = (uint16_t *)&reg_read;

                tx_buf[0] = rx_buf[0];
                tx_buf[1] = 0x41;
                tx_buf[2] = 0x01;
                tx_buf[3] = 0x00;
                tx_buf[4] = 19 * 2;
                uint16_t *pmbreg = &tx_buf[5];
                for (int i = 0; i < 19; i++)
                {
                    pmbreg[i] = mb_invert_u16(preg_base[i]);
                }
                size = 5 + 19 * 2 + 2;

                uint16_t crc = CRC16_Modbus(tx_buf, size - 2);
                tx_buf[size - 2] = crc & 0xFF;
                tx_buf[size - 1] = (crc >> 8) & 0xFF;

                rs485_send_frame(tx_buf, size);
            }
            else
                size = mb_slv_pack_respond_err(tx_buf, rx_buf[1] + MY_MODBUS_FUNC_ERR, 0x01);
            break;
        case MY_MODBUS_FUNC_0x01:
            preg_base = (uint16_t *)&reg_faultIO;

            if ((reg_addr == 0) && (reg_nums <= 16))
                size = mb_slv_pack_respond(tx_buf,
                                           rx_buf[1],
                                           NULL,
                                           1,
                                           &preg_base[0]
                                          );
            else
                mb_slv_pack_respond_err(tx_buf, rx_buf[1] + MY_MODBUS_FUNC_ERR, 0x03);
            break;
        case MY_MODBUS_FUNC_0x03:
        case MY_MODBUS_FUNC_0x04:

            if (preg_base != RT_NULL)
            {
                size = mb_slv_pack_respond(tx_buf,
                                           rx_buf[1],
                                           NULL,
                                           reg_nums,
                                           &preg_base[offset]
                                          );
            }
            else
            {
                if (reg_addr <= 32)
                    size = mb_slv_pack_respond_err(tx_buf, rx_buf[1] + MY_MODBUS_FUNC_ERR, 0x03);
                else
                    size = mb_slv_pack_respond_err(tx_buf, rx_buf[1] + MY_MODBUS_FUNC_ERR, 0x02);
            }
            break;

        case MY_MODBUS_FUNC_0x06:
            reg_data = mb_invert_u16(*(uint16_t *)&rx_buf[4]);

            if (preg_base != RT_NULL)
            {
                *preg_base = reg_data;

                my_modbus_write_reg(reg_addr, reg_data);

                size = mb_slv_pack_respond(tx_buf,
                                           MY_MODBUS_FUNC_0x06,
                                           reg_addr,
                                           1,
                                           &reg_data
                                          );
            }
            else
                size = mb_slv_pack_respond_err(tx_buf, rx_buf[1] + MY_MODBUS_FUNC_ERR, 0x02);

            break;

        case MY_MODBUS_FUNC_0x10:
            //unpack
            reg_bytes = rx_buf[6];

            if (size != (reg_nums * 2 + 9))
                break;

            uint8_t i = 0;

            while (i < reg_nums)
            {
                reg_data = mb_invert_u16(*(uint16_t *)&rx_buf[7 + i * 2]);
                preg_base = mdbus_regaddr_maptoreg((reg_addr + i),rx_buf[6]);

                if (preg_base != RT_NULL)
                {
                    *preg_base = reg_data;
                    my_modbus_write_reg((reg_addr + i), reg_data);
                }
                else
                {
                    err_flag = 1;
                    break;
                }

                i++;
            }

            if (err_flag == 0)
                size = mb_slv_pack_respond(tx_buf,
                                           MY_MODBUS_FUNC_0x10,
                                           reg_addr,
                                           reg_nums,
                                           &reg_data
                                          );
            else
                size = mb_slv_pack_respond_err(tx_buf, rx_buf[1] + MY_MODBUS_FUNC_ERR, 0x03);
            break;

        default:

            size = mb_slv_pack_respond_err(tx_buf, rx_buf[1], 0x01);
            vfd.modbus_rx_add_err_cnter++;

            break;

        }

        com_cnter_send(&com_modbus);
        com_cnter_recv(&com_modbus);

        rs485_send_frame(tx_buf, size);
        vfd.modbus_tx_cnter++;
    }

    return 0;
}