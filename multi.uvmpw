<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<ProjectWorkspace xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_mpw.xsd">

  <SchemaVersion>1.0</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <WorkspaceName>WorkSpace</WorkspaceName>

  <project>
    <PathAndName>.\bootloader\bsp\stm32\stm32g474\boot.uvprojx</PathAndName>
  </project>

  <project>
    <PathAndName>.\main\bsp\stm32\stm32g474\vfd.uvprojx</PathAndName>
    <NodeIsActive>1</NodeIsActive>
  </project>

  <project>
    <PathAndName>.\main\bsp\stm32\stm32g474\vfd_test.uvprojx</PathAndName>
    <NodeIsExpanded>1</NodeIsExpanded>
  </project>

</ProjectWorkspace>
