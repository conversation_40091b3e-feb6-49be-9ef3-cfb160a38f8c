/******************** (C) COPYRIGHT 2021     ***********************************
* File Name          : app_app.c
* Author             : xiou
* Version            : V1.0
* Date               :
* Description        : this is vfd system main logic
                        * achieve at least init/logic/control/start/stop ...

********************************************************************************/
#include <rtthread.h>
#include <rtdevice.h>

#define UAPP_MACRO
#include "uapp.h"

#include <board.h>

#define DBG_SECTION_NAME               "uapp"
#define DBG_LEVEL                      DBG_LOG
#include <rtdbg.h>

/* Private variables ------------------------------------------------------------*/

/* Private variables end---------------------------------------------------------*/

/* Private function prototypes --------------------------------------------------*/
static void vfd_in_pin_read(void);
static void vfd_out_pin_write(void);
void vfd_load_reducing(void);
extern int modbus_data_update(void);
int vfd_inv_mode(void);
/* Private function prototypes end-----------------------------------------------*/
int vfd_logdata_update(uint8_t *buff);
int vfd_logrtc_update(uint8_t *buff);
extern uint32_t pvd_it_cnt;
void vfd_record_st(void);
void vfd_condition_delay(void);
int vfd_start_work(void);
int vfd_conrol(uint8_t mode);
uint8_t vfd_StateSwtichTo(uint8_t *sys_st, uint8_t to);

static void vfd_clear_diagstopcnter(void);

int vfd_state_code_update(void);


/**
  * @brief vfd_init
  * @param
  * @retval
  */
int vfd_init(void)
{
    vfd.soft_ver_main    = VFD_SOFT_MAIN;
    vfd.soft_ver_sub     = VFD_SOFT_SUB;
    vfd.soft_ver_fixup   = VFD_SOFT_FIXUP;
    vfd.soft_ver_test    = 0;
    vfd.voltInputType    = 6;//undefined
    vfd.log_size = sizeof(record_log_t);

    record_logdata_update_hookset(vfd_logdata_update);
    record_logrtc_update_hookset(vfd_logrtc_update);

    vfd.dcIinCoeff = 31.25f;
    //vfd.test_code = 1;
}
extern void rt_acce_lis3dh_read(void);
#include "SystemDefine.h"
int vfd_machine_task(void)
{
    static uint8_t prev_st = 0;
    static uint32_t st_cnt;
    uint8_t manual_set_flag = 0;

    vfd.capacity_out = F32_FoLPFilter(vfd.capacity_out, mcsdk.MotorPower, 0.05f, 500.0f);
    vfd.capacity_in  = vfd.capacity_out;

    modbus_data_update();

    vfd_condition_delay();

    vfd_clear_diagstopcnter();

    vfd_record_st();
    
    rt_acce_lis3dh_read();

    vfd_load_reducing();

    vfd_state_code_update();
    // state exchange
    if (VFD_INPUT_24V || VFD_INPUT_NULL)
    {
        if (INVERTER_IS_RUN) vfd.bit.novolt_warning_flag = 1;
        vfd.ctrl.sys_st = ST_STOP;
    }
    else if (vfd.bit.lock_stop)
    {
        vfd.ctrl.sys_st = ST_ERROR_LOCK;
    }
    else if (vfd_get_fault_stop())
    {
        vfd.ctrl.sys_st = ST_ERROR;
    }
    else if (vfd.bit.soft_stop           ||
             vfd.is_update_fw            ||
             (vfd.ctrl.start == 0)       ||
             (vfd.ctrl.set_freq == 0)
            )
    {
        vfd.ctrl.sys_st = ST_STOP;
    }

    st_cnt++;
    if (prev_st != vfd.ctrl.sys_st)
    {
        prev_st = vfd.ctrl.sys_st;
        st_cnt = 0;

    }
#if (DEBUG_USE_IN123_CMD)
    manual_set_flag = 1;
#else
    // state control
    manual_set_flag = vfd.manual.start_vfd_mask || vfd.manual.start_boost_mask ;
#endif
    if (manual_set_flag == 0)
    {
        switch (vfd.ctrl.sys_st)
        {
        case ST_STOP:

            vfd.ctrl.start_vfd = 0;

            if (vfd.ctrl.start &&
                    (!VFD_INPUT_24V && !VFD_INPUT_NULL)
               )
            {
                vfd.ctrl.sys_st = ST_START;
            }

            break;

        case ST_START:

            if (vfd.bit.kmon_actived
                    && vfd.bit.vin_ready
                    && (mcsdk.CtrlMode != CTRL_MODE_STOP)
               )
            {
                if (vfd.bit.stop_ready || vfd.manual.start_mask || vfd.manual.in1_mask)
                    vfd.ctrl.start_vfd = 1;
            }
            else
            {
                if (!vfd.bit.vin_ready && INVERTER_IS_RUN) vfd.bit.novolt_warning_flag = 1;
                vfd.ctrl.start_vfd = 0;
            }



            break;

        case ST_ERROR:

            vfd.ctrl.start_vfd = 0;

            if (!vfd_get_fault_stop())
                vfd.ctrl.sys_st = ST_STOP;
            break;

        case ST_ERROR_LOCK:

            vfd.ctrl.start_vfd = 0;

            if (!vfd.bit.lock_stop)
                vfd.ctrl.sys_st = ST_STOP;

            break;

        default:
            vfd.ctrl.start_vfd = 0;

            vfd.ctrl.sys_st = ST_STOP;
            break;
        }
    }

    if (vfd.ctrl.start_vfd && vfd.ctrl.kmon)
        mcsdk.ops->start();
    else
    {
        mcsdk.ops->stop();
    }

    static uint32_t inputNullDelay = 0;
    static uint32_t inputPrechargeDelay = 0;
    if (!INVERTER_IS_RUN && vfd.diag.dc_60)
    {
        vfd.ctrl.kmon = 0;
        inputPrechargeDelay = 0;
    }
    else if (!INVERTER_IS_RUN && VFD_INPUT_NULL)
    {
        if (inputNullDelay < 10) inputNullDelay++;
        else vfd.ctrl.kmon = 0;
        inputPrechargeDelay = 0;
    }
    else if (!INVERTER_IS_RUN
             && (vfd.fast_ad.vbus_inv  <  250)
             && (vfd.fast_ad.acVinR    <  50.0f)
             && (mcsdk.MCstoptime_cnt  > 10)
            )
    {
        vfd.ctrl.kmon = 0;
        inputPrechargeDelay = 0;
    }
    else
    {
        inputNullDelay = 0;

        if (inputPrechargeDelay <= 200)  inputPrechargeDelay++;

        if ((((vfd.fast_ad.dcVin < 250.0f) && VFD_INPUT_DC)
                || (vfd.fast_ad.vbus_inv < 250.0f))
           )
        {
            vfd.bit.vin_ready = 0;

            if ((vfd.ctrl.kmon == 1) && !INVERTER_IS_RUN && (mcsdk.MCstoptime_cnt  > 10))
                vfd.ctrl.kmon = 0;
        }
        else if (vfd.bit.vbus_ready &&
                 (inputPrechargeDelay >= 200) &&
                 !vfd.diag.dc_40              &&
                 (((vfd.fast_ad.dcVin >= 400.0f) && VFD_INPUT_DC)
                  || (!VFD_INPUT_DC))
                )
        {
            vfd.bit.vin_ready = 1;
            vfd.ctrl.kmon = 1;
        }
    }


    static uint32_t start_tick;
    static uint8_t start_timeout_flag = 0;
    if (VFD_ENABLE && !INVERTER_IS_RUN)
    {
        if (start_tick <= 6000)
            start_tick++;
        else
            start_timeout_flag = 1;
    }
    else
    {
        start_tick = 0;
        start_timeout_flag = 0;
    }
    
    uint8_t fron_flag = (vfd.io.rev_overrpm_flag || start_timeout_flag || vfd_get_fault_stop() || (rt_tick_get() <= 10* RT_TICK_PER_SECOND)) ;
    
    vfd.ctrl.o1 = (fron_flag) ? 0 : 1;// 故障信号：无电或者故障断开，正常吸合

    return 0;
}


void vfd_load_reducing(void)
{
    if (vfd.diag.dc_03 || vfd.diag.dc_04|| vfd.diag.dc_05)
    {
        vfd.ctrl.load_reducing = 1;
    }
    else
    {
        vfd.ctrl.load_reducing = 0;
    }

}

extern mean_filter_t dcvinR_mf_s;
extern mean_filter_t dcvinS_mf_s;
extern mean_filter_t dcvinT_mf_s;
void vfd_condition_delay(void)
{
    static uint8_t power_on__wait = 0;
    static uint32_t vbus_ready_cnt = 0;
    static uint32_t vinbus_ready_cnt = 0;
    static uint32_t vac_ready_cnt = 0;
    static uint32_t vac_notready_cnt = 0;
    static uint32_t kmon_ready_cnt = 0;
    static uint32_t stop_ready_cnt = 2000;

    uint8_t active = 0;

    if (rt_tick_get() > 2 * RT_TICK_PER_SECOND)    power_on__wait = 1;

    if (!INVERTER_IS_RUN)
    {
        if (stop_ready_cnt < 500) stop_ready_cnt++;
        else
            vfd.bit.stop_ready = 1;
    }
    else
    {
        vfd.bit.stop_ready = 0;
        stop_ready_cnt = 0;
    }

    active = ((vfd.fast_ad.vbus_inv  >=  400)\
              && !VFD_INPUT_NULL);
    vfd.bit.vbus_ready   = single_filter(vfd.bit.vbus_ready,    active, &vbus_ready_cnt, 200);

    if (vfd.fast_ad.acVinR >= 200.0f)
    {
        vac_notready_cnt = 0;
        if (vac_ready_cnt <= 200)
            vac_ready_cnt++;
        else
            vfd.bit.acvin_ready = 1;
    }
    else if (vfd.fast_ad.acVinR < 50.0f)
    {
        vac_ready_cnt = 0;
        if (vac_notready_cnt < 10)
            vac_notready_cnt++;
        else
            vfd.bit.acvin_ready = 0;
    }

    if (vfd.ctrl.kmon)
    {
        if (kmon_ready_cnt <= 100)
            kmon_ready_cnt++;
        else
            vfd.bit.kmon_actived = 1;
    }
    else
    {
        kmon_ready_cnt = 0;
        vfd.bit.kmon_actived = 0;
    }


    static uint16_t voltInput1_cnt = 0;
    static uint16_t voltInput2_cnt = 0;
    static uint16_t voltInput3_cnt = 0;
    static uint16_t voltInput4_cnt = 0;
#define INPUT_FILT_TICK 50
    if ((((vfd.fast_ad.acVinR >= 400) && (vfd.fast_ad.acVinS >= 400)
            && (vfd.fast_ad.acVinT >= 400) && (vfd.acin_absfreq < 10))
            || (!INVERTER_IS_RUN && (vfd.fast_ad.ac_iout_u > 15.0f) && (vfd.fast_ad.ac_iout_v > 15.0f)))
            && (vfd.fast_ad.dc_5V >= 4.0f)
            && (vfd.fast_ad.dc_15V < 5.0f))
    {
        voltInput1_cnt = 0;
        voltInput2_cnt = 0;
        voltInput3_cnt = 0;
        voltInput4_cnt = 0;

        vfd.voltInputType = 5; //24V
    }
    else if ((((vfd.filter_ad.acVinR >= 50) && (vfd.filter_ad.acVinS >= 50))
              || ((vfd.filter_ad.acVinR >= 50) && (vfd.filter_ad.acVinT >= 50))
              || ((vfd.filter_ad.acVinS >= 50) && (vfd.filter_ad.acVinT >= 50)))
             && (VacRin.is_ac_flag  || VacSin.is_ac_flag)
            )
    {
        if (voltInput1_cnt < INPUT_FILT_TICK) voltInput1_cnt++;
        else
        {
            voltInput4_cnt = 0;
            voltInput2_cnt = 0;
            voltInput3_cnt = 0;
            vfd.voltInputType = 1; //AC
        }
    }
    else if (((vfd.dcvin_index == 2) || (vfd.dcvin_index == -2)) &&
             (fabs(dcvinS_mf_s.filtVal) > 50.0f) &&
             (vfd.fast_ad.dcVin >= 100) &&
             (vfd.acin_absfreq < 10)     &&
             (!vfd.diag.dc_31 && !vfd.diag.dc_22 && !vfd.diag.dc_23 && !vfd.diag.dc_24)
            )
    {
        if (voltInput2_cnt < 2 * INPUT_FILT_TICK) voltInput2_cnt++;
        else
        {
            voltInput1_cnt = 0;
            voltInput4_cnt = 0;
            voltInput3_cnt = 0;
            vfd.voltInputType = 3; //DC
        }
    }
    else if ( ((vfd.dcvin_index == 1) || (vfd.dcvin_index == -1)) &&
             (fabs(dcvinR_mf_s.filtVal) > 50.0f) &&
             (vfd.fast_ad.dcVin >= 100) &&
             (vfd.acin_absfreq < 10)    &&
             (!vfd.diag.dc_31 && !vfd.diag.dc_22 && !vfd.diag.dc_23 && !vfd.diag.dc_24)
            )
    {
        if (voltInput3_cnt < 2 * INPUT_FILT_TICK) voltInput3_cnt++;
        else
        {
            voltInput1_cnt = 0;
            voltInput2_cnt = 0;
            voltInput4_cnt = 0;
            vfd.voltInputType = 2; //DC
        }
    }
    else if (((vfd.dcvin_index == 3) || (vfd.dcvin_index == -3)) &&
             (fabs(dcvinT_mf_s.filtVal) > 50.0f) &&
             (vfd.fast_ad.dcVin >= 100) &&
             (vfd.acin_absfreq < 10) &&
             (!vfd.diag.dc_31 && !vfd.diag.dc_22 && !vfd.diag.dc_23 && !vfd.diag.dc_24)
            )
    {
        if (voltInput4_cnt < 2 * INPUT_FILT_TICK) voltInput4_cnt++;
        else
        {
            voltInput1_cnt = 0;
            voltInput2_cnt = 0;
            voltInput3_cnt = 0;
            vfd.voltInputType = 4; //DC
        }
    }
    else if ((vfd.fast_ad.acVinR < 50) && (vfd.fast_ad.acVinS < 50) && (vfd.fast_ad.acVinT < 50)
             && ((vfd.fast_ad.dcVin < 50)))
    {
        voltInput1_cnt = 0;
        voltInput2_cnt = 0;
        voltInput3_cnt = 0;
        voltInput4_cnt = 0;

        vfd.voltInputType = 0; // NULL
    }
    else
    {
        voltInput1_cnt = 0;
        voltInput4_cnt = 0;
        voltInput2_cnt = 0;
        voltInput3_cnt = 0;
        vfd.voltInputType = 6; // undefine
    }

}



void vfd_record_st(void)
{
    static uint8_t prev_sys_st = 0;
    static uint8_t prev_inv_st = 0;
    static uint8_t prev_mc_st = 0;

    if (prev_sys_st != vfd.ctrl.sys_st)
    {
        record_logdata_push(LOG_SysSt, vfd.ctrl.sys_st);

        prev_sys_st = vfd.ctrl.sys_st;
    }

    vfd.ctrl.set_speed   = abs(motor_get_rpmSetOut()); //5 poles
    vfd.ctrl.mcsdk_st    = mcsdk.MCState;
    vfd.ctrl.motor_speed = mcsdk.MotorSpeed;
    vfd.ctrl.motor_freq  = mcsdk.Motorfreq;
}

/**
  * @brief vfd_io_control
  * @param
  * @retval
  */
u16 debugTorqueCnt;
u16 debugTorqueFlg;
int vfd_state_code_update(void)
{
    static uint16_t torque_max_cnt = 0;

    if (mcsdk.torque_max && !mcsdk.tune_12 && !mcsdk.tune_11)
    {
        if (torque_max_cnt <= 100)
        {
            torque_max_cnt++;
        }
    }
    else
        torque_max_cnt = 0;

    debugTorqueCnt = torque_max_cnt;
    if (vfd.bit.lock_stop)
        vfd.state_code = 6;
    else if (vfd.bit.diag_lvl_1 || vfd.bit.diag_lvl_2)
        vfd.state_code = 7;
    else if (vfd.io.in1 && !vfd.bit.acvin_ready)
        vfd.state_code = 3;//4 .5
    else if (vfd.ctrl.load_reducing)
        vfd.state_code = 5;
    else if (torque_max_cnt >= 100)
    {
        debugTorqueFlg++;
        vfd.state_code = 1;
    }
    else
        vfd.state_code = 0;


    return 0;

}
/**
  * @brief vfd_io_control
  * @param
  * @retval
  */
int vfd_io_control(void)
{

    vfd_in_pin_read();  // - ��ȡ�������ŵ�ƽ
    vfd_out_pin_write();

    vfd.bit.com_can = com_can.flag_normal;
    vfd.bit.com_485 = com_485.flag_normal;

}

/**
  * @brief vfd_operation_statistics
  * @param
  * @retval
  */
int vfd_operation_statistics(void)
{
    static uint8_t first = 1;
    static uint8_t last_acvin_ready = 0;
    static uint8_t last_dcin_ready = 0;
    static uint8_t last_buskmon = 0;
    static uint8_t last_dckmon = 0;
    static uint8_t last_run = 0;
    static uint32_t sec_tick = 0;
    float power = 0;

    if (rt_tick_get() / RT_TICK_PER_SECOND != sec_tick)
    {
        sec_tick = rt_tick_get() / RT_TICK_PER_SECOND;

        if (first)
        {
            first = 0;
            nvs_datas.accumulator.dataU32[3]++; // power on times
            nvs_datas.accumulator.dataU32[0] += rt_tick_get() / RT_TICK_PER_SECOND; 
        }
        else
            nvs_datas.accumulator.dataU32[0]++; // power on seconds

        if (vfd.bit.acvin_ready != last_acvin_ready)
        {
            last_acvin_ready = vfd.bit.acvin_ready;
            if (vfd.bit.acvin_ready)
                nvs_datas.accumulator.dataU32[4]++; // acvin on times
        }

        if (VFD_INPUT_DC != last_dcin_ready)
        {
            last_dcin_ready = VFD_INPUT_DC;
            if (VFD_INPUT_DC)
                nvs_datas.accumulator.dataU32[5]++; // dcvin on times
        }

        if (vfd.ctrl.kmon != last_buskmon)
        {
            last_buskmon = vfd.ctrl.kmon;
            if (vfd.ctrl.kmon)
                nvs_datas.accumulator.dataU32[6]++; // vbus kmon on times
        }



        if (INVERTER_IS_RUN)
        {
            rt_int16_t temp = 0;
            
            float power = vfd.capacity_out;//W

            power = power / 3600; //W*h

            vfd.motor_run_sec++;

           
            if (VFD_INPUT_DC)
            {
                nvs_datas.accumulator.dataF32[1] += power; // DC consumption
                nvs_datas.accumulator.dataU32[2]++;
            }    
            else
            {
                nvs_datas.accumulator.dataF32[0] += power; // AC consumption
                nvs_datas.accumulator.dataU32[1]++;
            }

            vfd.powerconsumption_now    += power;

            if ((vfd.ad.temp_cap) < 400)       nvs_datas.accumulator.dataU32[8]++;      // <40度
            else if ((vfd.ad.temp_cap) < 500)   nvs_datas.accumulator.dataU32[9]++;      // 40-50度
            else if ((vfd.ad.temp_cap) < 600)   nvs_datas.accumulator.dataU32[10]++;     // 50-60度 
            else if ((vfd.ad.temp_cap) < 700)   nvs_datas.accumulator.dataU32[11]++;     // 60-70度
            else if ((vfd.ad.temp_cap) < 800)   nvs_datas.accumulator.dataU32[12]++;     // 70-80度
            else if ((vfd.ad.temp_cap) < 900)   nvs_datas.accumulator.dataU32[13]++;     // 80-90度
            else if ((vfd.ad.temp_cap) < 1000)  nvs_datas.accumulator.dataU32[14]++;     // 90-100度
            else if ((vfd.ad.temp_cap) >= 1000) nvs_datas.accumulator.dataU32[15]++;     // >100度

            if ((vfd.ad.temp_inv_dcl) < 400)       nvs_datas.accumulator.dataU32[16]++;      // <40度
            else if ((vfd.ad.temp_inv_dcl) < 500)   nvs_datas.accumulator.dataU32[17]++;      // 40-50度
            else if ((vfd.ad.temp_inv_dcl) < 600)   nvs_datas.accumulator.dataU32[18]++;     // 50-60度 
            else if ((vfd.ad.temp_inv_dcl) < 700)   nvs_datas.accumulator.dataU32[19]++;     // 60-70度
            else if ((vfd.ad.temp_inv_dcl) < 800)   nvs_datas.accumulator.dataU32[20]++;     // 70-80度
            else if ((vfd.ad.temp_inv_dcl) < 900)   nvs_datas.accumulator.dataU32[21]++;     // 80-90度
            else if ((vfd.ad.temp_inv_dcl) < 1000)  nvs_datas.accumulator.dataU32[22]++;     // 90-100度
            else if ((vfd.ad.temp_inv_dcl) >= 1000) nvs_datas.accumulator.dataU32[23]++;     // >100度

            if (vfd.capacity_out < 300.0f)          nvs_datas.accumulator.dataU32[24]++; // 0-300
            else if (vfd.capacity_out < 400.0f)     nvs_datas.accumulator.dataU32[25]++; // 300-400
            else if (vfd.capacity_out < 500.0f)     nvs_datas.accumulator.dataU32[26]++; // 400-500
            else if (vfd.capacity_out < 600.0f)     nvs_datas.accumulator.dataU32[27]++; // 500-600
            else if (vfd.capacity_out < 700.0f)     nvs_datas.accumulator.dataU32[28]++; // 600-700
            else if (vfd.capacity_out < 800.0f)     nvs_datas.accumulator.dataU32[29]++; // 700-800
            else if (vfd.capacity_out < 900.0f)     nvs_datas.accumulator.dataU32[30]++; // 800-900
            else if (vfd.capacity_out < 1000.0f)    nvs_datas.accumulator.dataU32[31]++; // 900-1000
            else if (vfd.capacity_out < 1100.0f)    nvs_datas.accumulator.dataU32[32]++; // 1000-1100
            else if (vfd.capacity_out < 1200.0f)    nvs_datas.accumulator.dataU32[33]++; // 1100-1200
            else if (vfd.capacity_out < 1300.0f)    nvs_datas.accumulator.dataU32[34]++; // 1200-1300
            else if (vfd.capacity_out < 1400.0f)    nvs_datas.accumulator.dataU32[35]++; // 1300-1400
            else if (vfd.capacity_out < 1500.0f)    nvs_datas.accumulator.dataU32[36]++; // 1400-1500
            else if (vfd.capacity_out < 1600.0f)    nvs_datas.accumulator.dataU32[37]++; // 1500-1600
            else if (vfd.capacity_out < 1700.0f)    nvs_datas.accumulator.dataU32[38]++; // 1600-1700
            else if (vfd.capacity_out < 1800.0f)    nvs_datas.accumulator.dataU32[39]++; // 1700-1800
            else if (vfd.capacity_out < 1900.0f)    nvs_datas.accumulator.dataU32[40]++; // 1800-1900
            else if (vfd.capacity_out < 2000.0f)    nvs_datas.accumulator.dataU32[41]++; // 1900-2000
            else if (vfd.capacity_out < 2100.0f)    nvs_datas.accumulator.dataU32[42]++; // 2000-2100
            else if (vfd.capacity_out < 2200.0f)    nvs_datas.accumulator.dataU32[43]++; // 2100-2200
            else if (vfd.capacity_out < 2300.0f)    nvs_datas.accumulator.dataU32[44]++; // 2200-2300
            else if (vfd.capacity_out < 2400.0f)    nvs_datas.accumulator.dataU32[45]++; // 2300-2400
            else if (vfd.capacity_out < 2500.0f)    nvs_datas.accumulator.dataU32[46]++; // 2400-2500
            else if (vfd.capacity_out < 2600.0f)    nvs_datas.accumulator.dataU32[47]++; // 2500-2600
            else if (vfd.capacity_out < 2700.0f)    nvs_datas.accumulator.dataU32[48]++; // 2600-2700
            else if (vfd.capacity_out < 2800.0f)    nvs_datas.accumulator.dataU32[49]++; // 2700-2800
            else if (vfd.capacity_out < 2900.0f)    nvs_datas.accumulator.dataU32[50]++; // 2800-2900
            else if (vfd.capacity_out < 3000.0f)    nvs_datas.accumulator.dataU32[51]++; // 2900-3000
            else if (vfd.capacity_out >= 3000.0f)   nvs_datas.accumulator.dataU32[52]++; // >3000

        }
        else
        {
            vfd.powerconsumption_now = 0;
            vfd.motor_run_sec = 0;
        }    

        if((last_run != INVERTER_IS_RUN)
        && INVERTER_IS_RUN)
        {
            nvs_datas.accumulator.dataU32[7]++; // acout start times
        }

        static uint8_t last_o1 = 0;

        if(vfd.ctrl.o1 != last_o1)
        {
            last_o1 = vfd.ctrl.o1;
            if(vfd.ctrl.o1)
                nvs_datas.accumulator.dataU32[53]++;
        }

        last_run = INVERTER_IS_RUN;
    }
}


/**
  * @brief vfd_in_pin_read  - ��ȡ����������
  * @param
  * @retval
  */
static void vfd_in_pin_read(void)
{
    static uint16_t poweron_cnt = 0;


    // - �ϵ�2s��ȷ����ƽ
    if (poweron_cnt < 0xFFFF)
        poweron_cnt++;


    if (poweron_cnt <= 200)
    {
        // - 1.�����ŵ�ƽ    2.��ƽ����10�����ϲ�ȷ��һ����ƽ
        vfd.addr = vfd.io.add1 + (vfd.io.add2 << 1);

        vfd.ctrl_hw_ver = 10 ;// v1.0
    }
    else
        vfd.bit.io_init = 1;

    if (nvs_datas.config.serial_com_id != 0)
        vfd.serial_addr = nvs_datas.config.serial_com_id;
    else
        vfd.serial_addr = vfd.addr + VFD_SERIAL_BASE_ADDR;

    if (!vfd.bit.dio_init)
        return;


    static uint32_t bAddr1_cnt = 0;
    static uint32_t bAddr2_cnt = 0;

    // - 1.�����ŵ�ƽ    2.��ƽ����10�����ϲ�ȷ��һ����ƽ
    vfd.io.add1 = single_filter(vfd.io.add1, DIO_NAME_READ_BIT("ADDR1"), &bAddr1_cnt, 10 / 2);
    vfd.io.add2 = single_filter(vfd.io.add2, DIO_NAME_READ_BIT("ADDR2"), &bAddr2_cnt, 10 / 2);

    static uint32_t i1_filter = 0;
    static uint32_t i2_filter = 0;
    static uint32_t i3_filter = 0;
    static uint32_t i4_filter = 0;

    static uint8_t save_in1 = 0;
    vfd.io.in1 = (vfd.manual.in1_mask & vfd.manual.in1)  || (single_filter(vfd.io.in1, DIO_NAME_READ_BIT("IN1"), &i1_filter, 50) & !vfd.manual.in1_mask);
    vfd.io.in2 = (vfd.manual.in2_mask & vfd.manual.in2)  || (single_filter(vfd.io.in2, DIO_NAME_READ_BIT("IN2"), &i2_filter, 50) & !vfd.manual.in2_mask);
    vfd.io.in3 = (vfd.manual.in3_mask & vfd.manual.in3)  || (single_filter(vfd.io.in3, DIO_NAME_READ_BIT("IN3"), &i3_filter, 50) & !vfd.manual.in3_mask);

    vfd.io.led_err = DIO_NAME_READ_BIT("LED_FLT");
    vfd.io.led_run = DIO_NAME_READ_BIT("LED_RUN");
    
    if (vfd.io.in1 && (vfd.io.in1 != save_in1))
    {
        vfd.kmon1_try = 0;
    }

    save_in1 = vfd.io.in1;

    vfd.io.pow      = DIO_NAME_READ_BIT("POW");
    vfd.io.kmon     = DIO_NAME_READ_BIT("KMON");

    vfd.io.o1       = DIO_NAME_READ_BIT("O1");
    vfd.io.F_IPM    = DIO_NAME_READ_BIT("F_IPM");

    vfd.io.F_HD     = DIO_READ_BIT(F_HD_PIN);

    vfd.io.cbc_flag = (!DIO_NAME_READ_BIT("BREAKIN") || (DIO_NAME_READ_DELAY("BREAKIN") > 0));
    vfd.ctrl.o1 = (vfd.manual.o1_mask) ? (vfd.manual.o1) : (vfd.ctrl.o1);
    vfd.ctrl.o2 = (vfd.manual.o2_mask) ? (vfd.manual.o2) : (0);
    vfd.ctrl.o3 = (vfd.manual.o3_mask) ? (vfd.manual.o3) : (0);

    vfd.ctrl.kmon = (vfd.manual.kmon_mask) ? (vfd.manual.kmon) : vfd.ctrl.kmon;


    uint8_t in1_start_flag = vfd.io.in1;
    

    if (vfd.filter_ad.aux_ai1 >= 8.0f)
        vfd.io.aux_i1_start_flag = 1;
    else if (vfd.filter_ad.aux_ai1 <= 5.0f)
        vfd.io.aux_i1_start_flag = 0;

    vfd.ctrl.start = (vfd.manual.start_mask) ? (vfd.manual.start) : (in1_start_flag || vfd.io.aux_i1_start_flag);
    vfd.ctrl.start_vfd   = (vfd.manual.start_vfd_mask) ? (vfd.manual.start_vfd) : (vfd.ctrl.start_vfd);

    vfd.ctrl.start &= vfd.bit.kmon_actived;
    vfd.ctrl.start_vfd &= vfd.bit.kmon_actived;
}
#include "MotorInvProtectInclude.h"
/**
  * @brief vfd_out_pin_write  - ������
  * @param
  * @retval
  */
static void vfd_out_pin_write(void)
{
    static uint32_t clr_cnt = 0;
    static uint8_t  clr_bit = 0;

    clr_bit = disactive_filter(clr_bit, vfd.io.F_HD, &clr_cnt, 500);

    dio_pin_write("KMON", vfd.ctrl.kmon);
    dio_pin_write("O1", vfd.ctrl.o1);

}

/**
  * @brief vfd_clear_diagstopcnter  - ��������30s�����������������?
  * @param
  * @retval
  */
static void vfd_clear_diagstopcnter(void)
{
    static uint32_t clear_cnter = 0;
    static uint32_t speed_ready_cnter = 0;

    if (INVERTER_IS_RUN)
    {
        int16_t deta = 0;

        deta = abs(mcsdk.MotorSpeed - mcsdk.ControlSpeed);

        if (vfd.diag_stop_times == 0)
        {
            speed_ready_cnter = 0;
            clear_cnter = 0;
        }

        if ((vfd.diag_history[0] != 91) && (deta < 10))
        {
            if (speed_ready_cnter < 6000)
                speed_ready_cnter++;
        }
        else if (deta > 100)
            speed_ready_cnter = 0;

        if (speed_ready_cnter >= 3000)
        {
            vfd.diag_stop_times = 0;
            vfd.vbus_kmon_err_cnt = 0;
        }

        if ((vfd.diag_history[0] != 91) && (clear_cnter <= 30 * 100))
            clear_cnter++;
        else if (clear_cnter > 30 * 100)
        {
            vfd.diag_stop_times = 0;
            vfd.vbus_kmon_err_cnt = 0;
        }
    }
    else
    {
        clear_cnter = 0;
        speed_ready_cnter = 0;
    }
}

extern int32_t gSaveStartFreq;

int vfd_logdata_update(uint8_t *buff)
{
    log_data_t *log = (log_data_t *)buff;

    if (buff == RT_NULL)
        return -1;

    uint32_t data = 0;
    // offset + 23
    toolbox_u32_set_data(&buff[0], 1, vfd.serial_addr);                   //ͨ��ID
    toolbox_u32_set_data(&buff[1], 4, rt_tick_get() / RT_TICK_PER_SECOND); //�����ϵ�ʱ��
    toolbox_u32_set_data(&buff[5], 2, VFD_SOFT_VERSION);        // �����?
    toolbox_u32_set_data(&buff[7], 2, vfd.ctrl_hw_ver);        //Ӳ���汾
    toolbox_u32_set_data(&buff[9], 1, vfd.board_id);

    rt_memcpy(&buff[10], (uint8_t *)&vfd.io,   5);

    U32_SET_BIT(buff[13], 1, (!DIO_NAME_READ_BIT("F_IPM") || (DIO_NAME_READ_DELAY("F_IPM") > 0)));

    rt_memcpy(&buff[16], (uint8_t *)&vfd.bit,  5);
    
    rt_memcpy(&buff[22], (uint8_t *)&vfd.diag, 8);

    rt_memcpy(&buff[30], (uint8_t *)&vfd.ctrl, 18);

    toolbox_u32_set_data(&buff[48], 2, vfd.spi3_lis3dh.filter_xyz.data.acce.z);
    toolbox_u32_set_data(&buff[50], 2, (int16_t)vfd.filter_ad.acVinS);
    toolbox_u32_set_data(&buff[52], 2, (int16_t)vfd.filter_ad.acVinT);
    toolbox_u32_set_data(&buff[54], 2, (int16_t)(vfd.filter_ad.ac_iout_w * 10));
    toolbox_u32_set_data(&buff[56], 2, (int16_t)vfd.filter_ad.dcVin);
    toolbox_u32_set_data(&buff[58], 2, motor_get_FcApply());
    toolbox_u32_set_data(&buff[60], 2, (int16_t)(vfd.acin_freq_folp * 10));
    toolbox_u32_set_data(&buff[62], 2, (int16_t)(vfd.filter_ad.dc_3_3V * 10));       //3.3V��ѹ
    toolbox_u32_set_data(&buff[64], 2, (int16_t)(vfd.filter_ad.dc_5V * 10));         //5V��ѹ
    toolbox_u32_set_data(&buff[66], 2, (int16_t)(vfd.filter_ad.dc_15V * 10));        //15V��ѹ

    toolbox_u32_set_data(&buff[68], 2, (int16_t)vfd.filter_ad.acVinR);

    toolbox_u32_set_data(&buff[70], 2, (int16_t)vfd.filter_ad.vbus_inv);
    toolbox_u32_set_data(&buff[72], 1, (int16_t)vfd.acout_absfreq);
    toolbox_u32_set_data(&buff[74], 2, (int16_t)(vfd.filter_ad.ac_iout_u * 10));
    toolbox_u32_set_data(&buff[76], 2, (int16_t)(vfd.filter_ad.ac_iout_v * 10));
    toolbox_u32_set_data(&buff[78], 2, (int16_t)vfd.filter_ad.ac_vout);
    toolbox_u32_set_data(&buff[80], 1, funcCode.code.motorParaM1.elem.motorType);
    toolbox_u32_set_data(&buff[81], 1, vfd.voltInputType);

    toolbox_u32_set_data(&buff[82], 2, vfd.spi3_lis3dh.filter_xyz.data.acce.x);
    toolbox_u32_set_data(&buff[84], 2, vfd.spi3_lis3dh.filter_xyz.data.acce.y);
    
    toolbox_u32_set_data(&buff[86], 2, (uint16_t)vfd.filter_ad.acVoutU);
    toolbox_u32_set_data(&buff[88], 2, (uint16_t)vfd.filter_ad.acVoutV);
    toolbox_u32_set_data(&buff[90], 2, (uint16_t)vfd.filter_ad.acVoutW);
    toolbox_u32_set_data(&buff[94], 1, (int16_t)vfd.filter_ad.temp_mcu);        //MCU�¶�
    toolbox_u32_set_data(&buff[95], 1, (int16_t)vfd.filter_ad.temp_HDC1080);
    toolbox_u32_set_data(&buff[96], 1, (int16_t)vfd.filter_ad.moisture_HDC1080);
    toolbox_u32_set_data(&buff[97], 1, nvs_datas.motor.param_src);
    toolbox_u32_set_data(&buff[99], 1, (int16_t)vfd.filter_ad.temp_igbt);
    toolbox_u32_set_data(&buff[100], 1, (int16_t)vfd.filter_ad.temp_cap);
    toolbox_u32_set_data(&buff[101], 2, vfd.invc_err_code);
    toolbox_u32_set_data(&buff[103], 1, (int16_t)vfd.filter_ad.temp_inv_dcl);
    
    toolbox_u32_set_data(&buff[104], 2, (int16_t)gSaveStartFreq);
}

int vfd_logrtc_update(uint8_t *buff)
{
    log_head_t *log = (log_head_t *)buff;

    if (buff == RT_NULL)
        return -1;

    update_local_time_data();

    log->date.year  = vfd.date.year;
    log->date.month = vfd.date.month;
    log->date.day   = vfd.date.day;
    log->date.hour  = vfd.date.hour;
    log->date.min   = vfd.date.min;
    log->date.sec   = vfd.date.sec;

}


#include <rtthread.h>
#include "msh.h"
extern int  TemperatureCheck(uint32_t tempAd);


void cmd_vfd(int argc, char **argv)
{
    rt_kprintf("\r\n");
    if (argc >= 3)
    {
        int val = atoi(argv[2]);

        if (!rt_strcmp("fan", argv[1]))
        {
            if (val > 0)
                vfd_start_fan(val, NULL);
            else
                vfd_stop_fan();
        }
        else if (!rt_memcmp("inverter", argv[1], 3))
        {

            vfd.manual.start_vfd_mask = (val <= 1) ? 1 : 0;
            vfd.manual.start_vfd = val;
        }
        else if (!rt_strcmp("disp", argv[1]))
        {
            int val = atoi(argv[2]);
            vfd.disp_ctrl = val;
            if (val == 0)
                toolbox_resume_rtkprintf();

            if (argc >= 4)
            {
                int val = atoi(argv[3]);
#ifdef DEBUG_SERIAL_PLOT
                vfd.debug_data_index = val;
#endif
            }
        }
        else if (rt_strcmp("temp_debug", argv[1]) == 0)
        {
            float _rdn, _ntc, _v, temp2;
            for (uint32_t i = 0; i < 4096; i += 10)
            {
                int tempValue = TemperatureCheck(i << 4);
                _v = (float)i * 3.3f / 4096;
                _rdn = 0.39f;
                _ntc = (5 - _v) / (_v / _rdn);
                temp2 = ntc_convert_3470_5k(_ntc) * 0.1f ;
                rt_kprintf("Temperature AD[%d] to Degree[%d] Degree2[%d] \r\n", i, tempValue, (int)temp2);
            }
        }
        else
        {
            return;
        }

        rt_kprintf("vfd set %s to %d\r\n", argv[1], val);
    }



}
MSH_CMD_EXPORT_ALIAS(cmd_vfd, vfd, debug);


