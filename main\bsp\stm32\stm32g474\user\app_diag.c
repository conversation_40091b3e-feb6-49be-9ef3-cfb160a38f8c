/******************** (C) COPYRIGHT 2021     ***********************************
* File Name          : app_diag.c
* Author             : xiou
* Version            : V1.0
* Date               :
* Description        :
********************************************************************************/
#include <rtthread.h>
#include <rtdevice.h>
#include "stm32g4xx.h"

#include "uapp.h"
#include "app_diag.h"

static uint16_t poweron_tick = 0;
static uint32_t adon_tick = 0;
static float vac_advolt = 0;
#define TICK_PER_SECOND     100
#define TICK_PER_MIN        (TICK_PER_SECOND * 60)
#define DIAG_RESUME_CNT     (200) /*! 5s */

#define DN2TCMS  0x1
#define DN2REC   0x2
#define DN2LED   0x4

#define SOFT   0x1
#define HARD   0x2
////////////////////////////////////////////////////////////////////////////////
//------------------------------------------------------------------------------
// Name:        sys_diag
//------------------------------------------------------------------------------
// Description: This function can be used to create a time delay.
//------------------------------------------------------------------------------
// code:        diag code to save or show
//
//------------------------------------------------------------------------------
// level:      0:none 1:lock&stop 2:stop 3:only record
//  mask:      bit0: dnot2tcms
//             bit1: dnot2record
//             bit2: dnot2led
//  type:      bit0: software
//             bit1: hardware
// resume tick: 1=10ms,  tick last to clear  diag code
// happen tick: 1=10ms,  tick last to active diag code
// str name :  console print diag name info
//------------------------------------------------------------------------------
////////////////////////////////////////////////////////////////////////////////
const diag_t diag_table[] =
{
    /*  code   level    mask  tyep             happen              resume      str name  */
    /*                                         1=10ms              1=10ms                */

    {1,   LEV_3,   0, SOFT,     100,            100, "DiagLgc_DCPowerinLimit"},
    {2,   LEV_3,   0, SOFT,      10,            200, "DiagLgc_ACIoutLimit"},
    {3,   LEV_3,   0, SOFT,     100,            100, "DiagLgc_DCIinLimit"},
    {4,   LEV_3,   0, SOFT,     100,            200, "DiagLgc_OverTempt_1_Warn"},
    {5,   LEV_3,   0, SOFT,     100,            200, "DiagLgc_OverTempt_2_Warn"},
    {6,   LEV_3,   0, SOFT,     100,            200, "DiagLgc_OverTempt_3_Warn"},
    {7,   LEV_3,   0, SOFT,     100,            200, "DiagLgc_OverTempt_4_Warn"},
    {8,   LEV_3,   0, SOFT,     200,            500, "DiagLgc_OverMoisture"},
    {9,   LEV_1,   0, HARD,      10,            500, "DiagLgc_InverterIPM"},
    {10,  LEV_2,   0, SOFT,     200,            200, "DiagLgc_In2In3Conflit"},
    {11,  LEV_1,   0, HARD,       0,           1000, "DiagLgc_BoostIPM"},
    {12,  LEV_2,   0, SOFT,     100,            500, "DiagLgc_ACVin_Over"},
    {13,  LEV_2,   0, SOFT,     200,            500, "DiagLgc_ACVin_Low"},
    {14,  LEV_2,   0, SOFT,       0,            200, "DiagLgc_Vbus_Over1"},
    {15,  LEV_2,   0, SOFT,       0,            200, "DiagLgc_Vbus_Over2"},
    {16,  LEV_2,   0, SOFT,     200,            200, "DiagLgc_Vbus_Low"},
    {17,  LEV_2,   0, SOFT,    6000,            500, "DiagLgc_ACIin_Over1"},
    {18,  LEV_2,   0, SOFT,    1000,            500, "DiagLgc_ACIin_Over2"},
    {19,  LEV_2,   0, SOFT,    6000,            500, "DiagLgc_ACIout_Over1"},
    {20,  LEV_2,   0, SOFT,    1000,            500, "DiagLgc_ACIout_Over2"},
    {21,  LEV_2,   0, SOFT,       0,            500, "DiagLgc_DCVin_Over"},
    {22,  LEV_2,   0, SOFT,     200,            500, "DiagLgc_DCVin_Low"},
    {23,  LEV_2,   0, SOFT,      50,            500, "DiagLgc_ACVin_LosePhaseR"},
    {24,  LEV_2,   0, SOFT,      50,            500, "DiagLgc_ACVin_LosePhaseS"},
    {25,  LEV_2,   0, SOFT,      50,            500, "DiagLgc_ACVin_LosePhaseT"},
    {26,  LEV_1,   0, SOFT,     500,            500, "DiagLgc_ACIout_LosePhaseU"},
    {27,  LEV_1,   0, SOFT,     500,            500, "DiagLgc_ACIout_LosePhaseV"},
    {28,  LEV_1,   0, SOFT,     500,            500, "DiagLgc_ACIout_LosePhaseW"},
    {29,  LEV_1,   0, SOFT,     300,            500, "DiagLgc_Vbus_KMON"},
    {30,  LEV_1,   0, SOFT,     100,            500, "DiagLgc_DCVin_KMON1"},
    {31,  LEV_2,   0, SOFT,      50,            500, "DiagLgc_PrechargeResist"},
    {32,  LEV_3,   0, SOFT,     300,            500, "DiagLgc_ACVin_PhaseSeqc"},
    {33,  LEV_3,   0, SOFT,     300,            500, "DiagLgc_ACVin_FreqLow"},
    {34,  LEV_3,   0, SOFT,     300,            500, "DiagLgc_ACVin_FreqOver"},
    {35,  LEV_3,   0, SOFT,     100,            200, "DiagLgc_SpiFlashMountErr"},
    {36,  LEV_2,   0, SOFT,      45,            200, "DiagLgc_AD_Ref"},
    {37,  LEV_1,   0, SOFT,     100,            200, "DiagLgc_12V_Over"},
    {38,  LEV_2,   0, SOFT,     200,            200, "DiagLgc_12V_Low"},
    {39,  LEV_1,   0, SOFT,     100,            200, "DiagLgc_5V_Over"},
    {40,  LEV_1,   0, SOFT,     100,            200, "DiagLgc_5V_Low"},
    {41,  LEV_2,   0, SOFT,     200,            200, "DiagLgc_MainPowerMisWire"},
    {42,  LEV_1,   0, SOFT,      10,           1000, "DiagLgc_ACIout_GroudU"},
    {43,  LEV_1,   0, SOFT,      10,           1000, "DiagLgc_ACIout_GroudV"},
    {44,  LEV_1,   0, SOFT,      10,           1000, "DiagLgc_ACIout_GroudW"},
    {45,  LEV_2,   0, SOFT,     500,            500, "DiagLgc_VoltInError"},
    {46,  LEV_3,   0, HARD,      10,            500, "DiagLgc_dc24v"},
    {47,  LEV_3,   0, HARD,     200,            200, "DiagLgc_dcInputPhaseErr"},
    {48,  LEV_3,   0, SOFT,       0,            200, "DiagLgc_Com485Timeout"},
    {49,  LEV_3,   0, SOFT,       0,            200, "DiagLgc_ComCanTimeout"},
    {50,  LEV_3,   0, SOFT,     200,            200, "DiagLgc_dcInputLineErr"},
    {51,  LEV_3,   0, SOFT,     100,            500, "DiagLgc_OverTempt_5_Warn"},
    {52,  LEV_3,   0, SOFT,       0,            200, "DiagLgc_IPM_ResetError"},
    {53,  LEV_1,   0, HARD,       0,            500, "DiagLgc_OVP_P_BUS"},
    {54,  LEV_1,   0, HARD,       0,            500, "DiagLgc_ICP_P"},
    {55,  LEV_1,   0, HARD,       0,            500, "DiagLgc_ICP_N"},
    {56,  LEV_1,   0, HARD,       0,            500, "DiagLgc_OCP_P"},
    {57,  LEV_1,   0, HARD,       0,            500, "DiagLgc_OCP_N"},
    {58,  LEV_1,   0, HARD,      50,            500, "DiagLgc_F_HD"},
    {59,  LEV_1,   0, SOFT,      10,            500, "DiagLgc_DCDC_SoftStart"},
    {60,  LEV_3,   0, SOFT,     100,            500, "DiagLgc_AD1_SensorErr"},
    {61,  LEV_1,   0, SOFT,     100,            500, "DiagLgc_AD2_SensorErr"},
    {62,  LEV_1,   0, SOFT,     100,            500, "DiagLgc_AD3_SensorErr"},
    {63,  LEV_1,   0, SOFT,     100,            500, "DiagLgc_AD4_SensorErr"},
    {64,  LEV_1,   0, SOFT,     100,            500, "DiagLgc_AD5_SensorErr"},
    {65,  LEV_3,   0, SOFT,     100,            500, "DiagLgc_AD6_SensorErr"},
    {66,  LEV_3,   0, SOFT,     100,            500, "DiagLgc_AD7_SensorErr"},
    {67,  LEV_3,   0, SOFT,     100,            500, "DiagLgc_AD8_SensorErr"},
    {68,  LEV_2,   0, SOFT,     100,            500, "DiagLgc_POW"},
    {69,  LEV_1,   0, SOFT,       0,            500, "DiagLgc_MC_FOC_DURATION"},
    {70,  LEV_1,   0, SOFT,       0,            500, "DiagLgc_MC_OVER_VOLT"},
    {71,  LEV_1,   0, SOFT,       0,            500, "DiagLgc_MC_UNDER_VOLT"},
    {72,  LEV_1,   0, SOFT,       0,            500, "DiagLgc_MC_OVER_TEMP"},
    {73,  LEV_1,   0, SOFT,       0,            500, "DiagLgc_MC_START_UP"},
    {74,  LEV_1,   0, SOFT,       0,            500, "DiagLgc_MC_SPEED_FDBK"},
    {75,  LEV_1,   0, HARD,       0,            500, "DiagLgc_MC_BREAK_IN"},
    {76,  LEV_1,   0, SOFT,       0,            500, "DiagLgc_MC_SW_ERROR"},
    {77,  LEV_3,   0, SOFT,      100,           200, "DiagLgc_Temp1_SensorFailed"},
    {78,  LEV_3,   0, SOFT,      100,           200, "DiagLgc_Temp2_SensorFailed"},
    {79,  LEV_3,   0, SOFT,      100,           500, "DiagLgc_Temp3_SensorFailed"},
    {80,  LEV_3,   0, SOFT,      200,           500, "DiagLgc_Temp4_SensorFailed"},
    {81,  LEV_3,   0, SOFT,      200,           500, "DiagLgc_lis3dh_SensorFailed"},
    {82,  LEV_3,   0, SOFT,       10,           500, "DiagLgc_hdc1080_SensorFailed"},
    {83,  LEV_2,   0, SOFT,      100,           500, "DiagLgc_OverTempt_1_Stop"},
    {84,  LEV_2,   0, SOFT,      100,           500, "DiagLgc_OverTempt_2_Stop"},
    {85,  LEV_2,   0, SOFT,      100,           500, "DiagLgc_OverTempt_3_Stop"},
    {86,  LEV_2,   0, SOFT,      100,           500, "DiagLgc_OverTempt_4_Stop"},
    {87,  LEV_2,   0, SOFT,        0,           500, "DiagLgc_Lock_Stop"},
    {88,  LEV_1,   0, SOFT,      200,           500, "DiagLgc_INVCE_Errcode"},
    {89,  LEV_1,   0, SOFT,      100,           500, "DiagLgc_Opencircuit"},
    {90,  LEV_3,   0, SOFT,      100,           500, "DiagLgc_OverTempt_MCU_Warn"},
    {91,  LEV_1,   0, SOFT,        0,           500, "DiagLgc_OutOverLoad"},
    {92,  LEV_1,   0, SOFT,        0,           200, "DiagLgc_INVCE_Err02"},
    {93,  LEV_1,   0, SOFT,        0,           200, "DiagLgc_INVCE_Err03"},
    {94,  LEV_1,   0, SOFT,        0,           200, "DiagLgc_INVCE_Err04"},
    {95,  LEV_1,   0, SOFT,        0,           200, "DiagLgc_INVCE_Err05"},
    {96,  LEV_1,   0, SOFT,        0,           200, "DiagLgc_INVCE_Err06"},
    {97,  LEV_1,   0, SOFT,        0,           200, "DiagLgc_INVCE_Err07"},
    {98,  LEV_1,   0, SOFT,        0,           200, "DiagLgc_INVCE_Err08"},
    {99,  LEV_1,   0, SOFT,        0,           200, "DiagLgc_INVCE_Err09"},
    {100, LEV_1,   0, SOFT,        0,           200, "DiagLgc_INVCE_Err10"},
    {101, LEV_1,   0, SOFT,        0,           200, "DiagLgc_INVCE_Err11"},
    {102, LEV_1,   0, SOFT,        0,           200, "DiagLgc_INVCE_Err12"},
    {103, LEV_1,   0, SOFT,       10,           200, "DiagLgc_INVCE_Err13"},
    {104, LEV_1,   0, SOFT,        0,           200, "DiagLgc_INVCE_Err14"},
    {105, LEV_1,   0, SOFT,        0,           200, "DiagLgc_INVCE_Err17"},
    {106, LEV_1,   0, SOFT,        0,           200, "DiagLgc_INVCE_Err18"},
    {107, LEV_1,   0, SOFT,        0,           200, "DiagLgc_INVCE_Err19"},
    {108, LEV_1,   0, SOFT,        0,           200, "DiagLgc_INVCE_Err23"},
    {109, LEV_1,   0, SOFT,        0,           200, "DiagLgc_INVCE_Err30"},
    {110, LEV_1,   0, SOFT,        0,           200, "DiagLgc_INVCE_Err31"},
    {111, LEV_1,   0, SOFT,        0,           200, "DiagLgc_INVCE_Err40"},
    {112, LEV_1,   0, SOFT,        0,           200, "DiagLgc_INVCE_Err51"},
    {113, LEV_1,   0, SOFT,        0,           200, "DiagLgc_INVCE_Err64"},
    {114, LEV_1,   0, SOFT,        0,           200, "DiagLgc_INVCE_Err67"},
    {115, LEV_1,   0, SOFT,    60000,           500, "DiagLgc_ACIout_Overload110"},
    {116, LEV_1,   0, SOFT,    45000,           500, "DiagLgc_ACIout_Overload120"},
    {117, LEV_1,   0, SOFT,    30000,           500, "DiagLgc_ACIout_Overload130"},
    {118, LEV_1,   0, SOFT,    15000,           500, "DiagLgc_ACIout_Overload140"},
    {119, LEV_1,   0, SOFT,     9000,           500, "DiagLgc_ACIout_Overload150"},
    {120, LEV_1,   0, SOFT,     4500,           500, "DiagLgc_ACIout_Overload160"},
    {121, LEV_1,   0, SOFT,     1500,           500, "DiagLgc_ACIout_Overload170"},
    {122, LEV_1,   0, SOFT,      500,           500, "DiagLgc_ACIout_Overload180"},
    {123, LEV_1,   0, SOFT,      100,           500, "DiagLgc_ACIout_Overload190"},
    {124, LEV_3,   0, SOFT,     100,            500, "DiagLgc_AD9_SensorErr"},  // VacUout sensor error
    {125, LEV_3,   0, SOFT,     100,            500, "DiagLgc_AD10_SensorErr"}, // VacVout sensor error  
    {126, LEV_3,   0, SOFT,     100,            500, "DiagLgc_AD11_SensorErr"}, // VacWout sensor error
};
//1498 947

extern uint16_t motor_get_accDec_status(void);
/**
  * @brief
  * @param
  * @retval
  */
uint8_t diag_table_len(void)
{
    return (sizeof(diag_table) / sizeof(diag_t));
}

/**
  * @brief
  * @param
  * @retval
  */
diag_t *diag_table_ptr(void)
{
    return (diag_t *)diag_table;
}



/**
  * @brief
  * @param
  * @retval
  */
void diag_poweron_reset(void)
{
    hardware_irq_pin.fo1_flag = RT_FALSE;
    hardware_irq_pin.fo2_flag = RT_FALSE;
    hardware_irq_pin.fo3_flag = RT_FALSE;
    hardware_irq_pin.f_tim1_breakin_flag = RT_FALSE;
    hardware_irq_pin.f_tim8_breakin_flag = RT_FALSE;
    hardware_irq_pin.f_tim20_breakin_flag = RT_FALSE;
}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_dc92_dc114(void)
{
    vfd.diag.dc_91  = (mcsdk.error_code ==  2) ? 1 : 0;
    vfd.diag.dc_92  = (mcsdk.error_code ==  3) ? 1 : 0;
    vfd.diag.dc_93  = (mcsdk.error_code ==  4) ? 1 : 0;
    vfd.diag.dc_94  = (mcsdk.error_code ==  5) ? 1 : 0;
    vfd.diag.dc_95  = (mcsdk.error_code ==  6) ? 1 : 0;

    vfd.diag.dc_96  = ((mcsdk.error_code ==  7) && !vfd.diag.dc_35 && (vfd.fast_ad.dc_3_3V > 3.1f)) ? 1 : 0;
    vfd.diag.dc_97  = (mcsdk.error_code ==  8) ? 1 : 0;
    vfd.diag.dc_98  = (mcsdk.error_code ==  9) ? 1 : 0;
    vfd.diag.dc_99  = (mcsdk.error_code == 10) ? 1 : 0;
    vfd.diag.dc_100 = (mcsdk.error_code == 11) ? 1 : 0;
    vfd.diag.dc_101 = (mcsdk.error_code == 12) ? 1 : 0;
    vfd.diag.dc_102 = (mcsdk.error_code == 13) && (!vfd.diag.dc_25 && !vfd.diag.dc_26 && !vfd.diag.dc_27) ? 1 : 0;
    vfd.diag.dc_103 = (mcsdk.error_code == 14) ? 1 : 0;
    vfd.diag.dc_104 = (mcsdk.error_code == 17) ? 1 : 0;
    
    if(!vfd.diag.dc_63 && !vfd.diag.dc_62 && !vfd.diag.dc_61)
        vfd.diag.dc_105 = (!vfd.diag.dc_63 && !vfd.diag.dc_62 && !vfd.diag.dc_61)&&(mcsdk.error_code == 18) ? 1 : 0;
    vfd.diag.dc_106 = (mcsdk.error_code == 19) ? 1 : 0;
    
    vfd.diag.dc_107 = (!vfd.diag.dc_63 && !vfd.diag.dc_62 && !vfd.diag.dc_61) &&(mcsdk.error_code == 23) ? 1 : 0;
    
    vfd.diag.dc_108 = (mcsdk.error_code == 30) ? 1 : 0;
    vfd.diag.dc_109 = (mcsdk.error_code == 31) ? 1 : 0;
    vfd.diag.dc_110 = (mcsdk.error_code == 40) ? 1 : 0;
    vfd.diag.dc_111 = (mcsdk.error_code == 51) ? 1 : 0;
    vfd.diag.dc_112 = (mcsdk.error_code == 64) ? 1 : 0;
    vfd.diag.dc_113 = (mcsdk.error_code == 67) ? 1 : 0;

}

extern mean_filter_t dcvinR_mf_s;
/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_dcInputPhaseErr(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault, _IsNormal;
    uint16_t vbus = 0;

    if (!vfd.bit.ad_init)
        return;
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault = (vfd.voltInputType == 2) && (dcvinR_mf_s.filtVal < 0);

    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}
#include "MotorInvProtectInclude.h"
/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_IPM_ResetError(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault, _IsNormal;
    uint16_t vbus = 0;

    if (!vfd.bit.ad_init)
        return;
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault = (gIgbtBreake.ResetCnter >= 5);

    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_In2In3Conflit(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault, _IsNormal;
    uint16_t vbus = 0;

    if (!vfd.bit.ad_init)
        return;
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  0;

    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_dcInputLineErr(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault, _IsNormal;
    uint16_t vbus = 0;

    if (!vfd.bit.ad_init)
        return;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault = (vfd.voltInputType == 3) ||
               (vfd.voltInputType == 4) ;

    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}



/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_DCVin_Over(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault, _IsNormal;

    if (!vfd.bit.ad_init)
        return;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  VFD_INPUT_DC && (vfd.filter_ad.dcVin >= 720);

    _IsNormal = !VFD_INPUT_DC || (vfd.filter_ad.dcVin <= 715);

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_DCVin_Low(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault, _IsNormal;
    uint16_t dcvin = 0;
    if (!vfd.bit.ad_init)
        return;

    ///////////////////////////////////////////////////////////////////////////////////
    uint8_t normal_low_flag = 0;

    dcvin = vfd.filter_ad.dcVin * 0.1f;

    static float old_vin = 0;
    float delta  = fabs(vfd.filter_ad.dcVin - old_vin);

    if ((vfd.filter_ad.dcVin >= 250) || (delta > 20))
    {
        old_vin = vfd.filter_ad.dcVin;
        faultCnt = 0;
    }

    uint8_t input_low_flag = (delta < 20) && (vfd.filter_ad.dcVin <= 260) ;

    if (VFD_INPUT_DC && input_low_flag)
        normal_low_flag = 1;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  VFD_ENABLE && normal_low_flag && !vfd.diag.dc_15;

    _IsNormal = !_IsFault;


    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_dc24v(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault, _IsNormal;
    uint16_t vbus = 0;

    if (!vfd.bit.ad_init)
        return;
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  DC5V_Is_Ok && AV380_Is_Ok && VBUS_IS_Ok
                && ((vfd.filter_ad.dc_15V >= 20) || (vfd.ad.dc_15V <= 7));

    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}
/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_VoltInError(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault, _IsNormal;
    uint16_t vbus = 0;

    if (!vfd.bit.ad_init)
        return;
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  VFD_ENABLE && ((vfd.fast_ad.acVinR + vfd.fast_ad.acVinS + vfd.fast_ad.acVinT) < 100.0f) &&
                (vfd.fast_ad.vbus_inv < 100) && (vfd.fast_ad.dcVin < 100)
                && (VFD_INPUT_NULL || VFD_INPUT_UNDEF) && !vfd.diag.dc_40;

    if (vfd.bit.novolt_warning_flag)
    {
        _IsFault = 1;
        faultCnt  = diag_table[offset].happen_cnt;
        normalCnt = 0;
    }

    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);
    if (diag_bit)    vfd.bit.novolt_warning_flag = 0;
}

u16 acVinLoseFlag;
/**
  * @brief
  * @param
  * @retval
  */
static float maxVin;
void DiagLgc_ACVin_LosePhaseR(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault, _IsNormal;

    if (!vfd.bit.ad_init)
        return;

    
    float minVin1, minVin2;
    float vinR, vinS, vinT;

    vinR = vfd.fast_ad.acVinR;
    vinS = vfd.fast_ad.acVinS;
    vinT = vfd.fast_ad.acVinT;

    ///////////////////////////////////////////////////////////////////////////////////
    maxVin = (vinR > vinS) ? vinR : vinS;
    maxVin = (maxVin > vinT) ? maxVin : vinT;

    if (maxVin == vinR)
    {
        minVin1 = vinS;
        minVin2 = vinT;
        if ((maxVin / minVin1) > 1.3 && ((maxVin / minVin2) > 1.3))
            acVinLoseFlag = 3; //T LOSE
        else
            acVinLoseFlag = 0; //normal
    }
    else if (maxVin == vinS)
    {
        minVin1 = vinR;
        minVin2 = vinT;
        if ((maxVin / minVin1) > 1.3 && ((maxVin / minVin2) > 1.3))
            acVinLoseFlag = 1; //R LOSE
        else
            acVinLoseFlag = 0; //normal
    }
    else//if(maxVin==vinT)
    {
        minVin1 = vinR;
        minVin2 = vinS;
        if ((maxVin / minVin1) > 1.3 && ((maxVin / minVin2) > 1.3))
            acVinLoseFlag = 2; //S LOSE
        else
            acVinLoseFlag = 0; //normal
    }

    _IsFault =  (VFD_INPUT_AC)   && (acVinLoseFlag == 1) && (maxVin > 100);

    _IsNormal = !VFD_INPUT_AC || !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_ACVin_LosePhaseS(uint8_t *diag, int offset)
{
    static uint32_t faultCnt  = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault, _IsNormal;
    uint16_t vbus = 0;

    if (!vfd.bit.ad_init)
        return;
    _IsFault =  (VFD_INPUT_AC)   && (acVinLoseFlag == 2) && (maxVin > 100);

    _IsNormal = !VFD_INPUT_AC || !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_ACVin_LosePhaseT(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault, _IsNormal;
    uint16_t vbus = 0;

    if (!vfd.bit.ad_init)
        return;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  (VFD_INPUT_AC)   && (acVinLoseFlag == 3) && (maxVin > 100);

    _IsNormal = !VFD_INPUT_AC || !_IsFault;


    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_MainPowerMisWire(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    static float old_vin = 0;
    static uint8_t  stable_flag = 0;
    float delta  = fabs(vfd.filter_ad.vbus_inv - old_vin);

    if (delta > 5)
    {
        old_vin = vfd.filter_ad.vbus_inv;
        faultCnt = 0;
    }

    stable_flag = (delta < 5);

    _IsFault =  !vfd.diag.dc_22 && !vfd.diag.dc_23 && !vfd.diag.dc_24 && 
                !vfd.diag.dc_64 && !vfd.diag.dc_65 &&
                stable_flag
                && (((vfd.fast_ad.acVinR + vfd.fast_ad.acVinS + vfd.fast_ad.acVinT) < 100.0f) || VFD_INPUT_NULL )
                && (vfd.fast_ad.vbus_inv >= 400.0f);

    _IsNormal = (vfd.fast_ad.vbus_inv <= 50) || ((vfd.fast_ad.acVinR + vfd.fast_ad.acVinS + vfd.fast_ad.acVinT)/3 > 300.0f);

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);


}


/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_OverTempt_1_Warn(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;

    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault = (vfd.ad.temp_cap >= 950) && !vfd.diag.dc_76;

    _IsNormal = (vfd.ad.temp_cap <= 850) || vfd.diag.dc_76;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);
}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_OverTempt_2_Warn(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;

    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault = (vfd.ad.temp_igbt >= 1050) && !vfd.diag.dc_77;

    _IsNormal = (vfd.ad.temp_igbt <= 850) || vfd.diag.dc_77;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);
}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_OverTempt_3_Warn(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;

    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault = (vfd.ad.temp_inv_dcl >= 1050) && !vfd.diag.dc_77;

    _IsNormal = (vfd.ad.temp_inv_dcl <= 850) || vfd.diag.dc_77;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);
}



/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_OverTempt_1_Stop(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;

    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault = (vfd.ad.temp_cap >= 1000) && !vfd.diag.dc_76;

    _IsNormal = (vfd.ad.temp_cap <= 850) || vfd.diag.dc_76;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);
}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_OverTempt_2_Stop(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;

    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault = (vfd.ad.temp_igbt >= 1100) && !vfd.diag.dc_77;

    _IsNormal = (vfd.ad.temp_igbt <= 850) || vfd.diag.dc_77;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);
}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_OverTempt_3_Stop(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;

    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault = (vfd.ad.temp_inv_dcl >= 1100) && !vfd.diag.dc_77;

    _IsNormal = (vfd.ad.temp_inv_dcl <= 850) || vfd.diag.dc_77;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);
}
/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_OverMoisture(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;

    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault = (vfd.ad.moisture_HDC1080 >= 900) && (vfd.ad.temp_HDC1080 <= 1150) && (!vfd.diag.dc_81);

    _IsNormal = (vfd.ad.moisture_HDC1080 <= 850) || (vfd.diag.dc_81);

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);
}


/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_12V_Over(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;

    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    if (!vfd.bit.ad_init)
        return;
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault = (vfd.fast_ad.dc_15V >= 20) && !vfd.diag.dc_35;

    _IsNormal = (vfd.fast_ad.dc_15V <= 18);

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);
}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_12V_Low(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;

    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    if (!vfd.bit.ad_init)
        return;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault = (vfd.fast_ad.dc_15V <= 7) && !VFD_INPUT_24V;

    _IsNormal = (vfd.fast_ad.dc_15V >= 9);

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);
}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_5V_Over(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;

    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    if (!vfd.bit.ad_init)
        return;
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault = (vfd.fast_ad.dc_5V >= 5.5f) && !vfd.diag.dc_35;

    _IsNormal = (vfd.fast_ad.dc_5V <= 5.3f);

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);
}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_5V_Low(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;

    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    if (!vfd.bit.ad_init)
        return;
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault = (vfd.fast_ad.dc_5V <= 4.5f);

    _IsNormal = (vfd.fast_ad.dc_5V >= 4.7f);

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);
}



/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_Vbus_Over1(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;
    uint16_t vbus = 0;

    if (!vfd.bit.ad_init)
        return;

    //vbus = vfd.ad.vbus_inv/10;
    vbus = (uint16_t)vfd.fast_ad.vbus_inv;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault = (vbus >= 720) && (vbus < 800) && (!vfd.diag.dc_35) && (vfd.fast_ad.dc_3_3V > 3.0f);

    _IsNormal = (vbus <= 715);

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_Vbus_Over2(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;
    uint16_t vbus = 0;

    if (!vfd.bit.ad_init)
        return;

    //vbus = vfd.ad.vbus_inv/10;
    vbus = (uint16_t)vfd.fast_ad.vbus_inv;
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = (vbus >= 750) && (!vfd.diag.dc_35) && (vfd.fast_ad.dc_3_3V > 3.0f);

    _IsNormal = (vbus <= 715);

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}


/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_Vbus_Low(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;


    if (!vfd.bit.ad_init)
        return;

    uint8_t normal_low_flag = 0;
    uint16_t vbus = 0;
    vbus = vfd.ad.vbus_inv * 0.1f;

    static float old_vin = 0;
    float delta  = fabs(vfd.filter_ad.vbus_inv - old_vin);

    if ((vfd.ad.vbus_inv >= 2500) || (delta > 20))
    {
        old_vin = vfd.filter_ad.vbus_inv;
        faultCnt = 0;
    }

    uint8_t input_low_flag = (delta < 20) && (vfd.ad.vbus_inv <= 2600) ;

    if ((VFD_INPUT_DC || VFD_INPUT_AC) && input_low_flag)
        normal_low_flag = 1;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  VFD_ENABLE && normal_low_flag && !vfd.diag.dc_21;

    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_ACVin_Over(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    if (!vfd.bit.ad_init)
        return;

    float acvin = (vfd.filter_ad.acVinR + vfd.filter_ad.acVinS + vfd.filter_ad.acVinT) / 3;
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  =  VFD_ENABLE && (acvin >= 460) && !vfd.diag.dc_59;
    _IsFault &=  (VFD_INPUT_AC || VFD_INPUT_UNDEF);
    _IsNormal = (acvin <= 450) || vfd.diag.dc_59;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_ACVin_Low(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    if (!vfd.bit.ad_init)
        return;
    float acvin = (vfd.filter_ad.acVinR + vfd.filter_ad.acVinS + vfd.filter_ad.acVinT) / 3;
    static float old_vin = 0;
    float delta  = fabs(acvin - old_vin);

    if ((acvin >= 335) || (delta > 20))
    {
        old_vin  = acvin;
        faultCnt = 0;
    }

    uint8_t input_low_flag = (delta < 20) && (acvin <= 325) && (acvin >= 50);

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  =  VFD_ENABLE && input_low_flag
                 && !vfd.diag.dc_22 && !vfd.diag.dc_23 && !vfd.diag.dc_24 && !vfd.diag.dc_59;
    _IsFault  &= (VFD_INPUT_AC || VFD_INPUT_UNDEF);
    _IsNormal = (acvin >= 335) || (acvin < 50);

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}



/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_ACIout_Over1(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    if (!vfd.bit.ad_init)
        return;

    ///////////////////////////////////////////////////////////////////////////////////
    float iout_avg = (vfd.filter_ad.ac_iout_u + vfd.filter_ad.ac_iout_v + vfd.filter_ad.ac_iout_w) / 3;

    float iout_overload1;
    float ibase;
    //ibase = (float)nvs_datas.motor.ratingCurrent/100;
    ibase = !NVS_VFD_WITH_DCDC ? 2.4f : 2.0f;
    ibase = (ibase < 0.1f)   ? 2.4f : ibase;

    iout_overload1 = ibase * 1.3f; // 130% overload

    _IsFault = (iout_avg >= iout_overload1);

    _IsNormal = (iout_avg <= ibase) ;


    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_ACIout_Over2(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    if (!vfd.bit.ad_init)
        return;

    ///////////////////////////////////////////////////////////////////////////////////
    ///////////////////////////////////////////////////////////////////////////////////
    float iout_avg = (vfd.filter_ad.ac_iout_u + vfd.filter_ad.ac_iout_v + vfd.filter_ad.ac_iout_w) / 3;

    float iout_overload2;
    float ibase;

    //ibase = (float)nvs_datas.motor.ratingCurrent/100;
    ibase = !NVS_VFD_WITH_DCDC ? 2.4f : 2.0f;
    ibase = (ibase < 0.1f)   ? 2.4f : ibase;

    iout_overload2 = ibase * 1.5f; // 150% overload

    _IsFault = (iout_avg >= iout_overload2);

    _IsNormal = (iout_avg <= ibase) ;


    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);
}






/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_OverTempt_5_Warn(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    if (!vfd.bit.ad_init)
        return;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault = (vfd.ad.temp_HDC1080 >= 1000) && (vfd.ad.temp_mcu >= 1000);

    _IsNormal = (vfd.ad.temp_HDC1080 <= 950) && (vfd.ad.temp_mcu <= 950);

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}

uint32_t vbus_max;
uint32_t vbus_min;
/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_Vbus_KMON(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal = 0;


    static uint8_t ticker;
    static uint32_t delta = 0;

    ticker++;
    if (ticker > 10)
    {
        delta = abs(vbus_max - vbus_min);
        ticker = 0;

        vbus_max = (uint32_t)vfd.fast_ad.vbus_inv * 10;
        vbus_min = (uint32_t)vfd.fast_ad.vbus_inv * 10;
    }

    _IsFault  = INVERTER_IS_RUN &&
                (((motor_get_accDec_status() == 0) && (delta > 2000))
                 || (delta > 2500))
                && vfd.ctrl.kmon
                && (vfd.filter_ad.vbus_inv >= 400.0f);

    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}



#define TEMPT_SENSOR_ERR_MCU      600       /* 60 �� ���¶�ֵ�����MCU�¶����60�棬����Ӧͨ���¶ȴ��������� */
#define TEMPT_SENSOR_ERR_VALUE    1240      /* 124 �� */
#define TEMPT_RESUME_VALUE        850       /* 85 �� */

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_Temp1_SensorFailed(uint8_t *diag, int offset)
{
    static uint32_t faultCnt  = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    int delta = abs(vfd.ad.temp_cap - vfd.ad.temp_mcu);

    if ((adon_tick <= 500))
        _IsFault = (delta >= TEMPT_SENSOR_ERR_MCU) || (vfd.ad.temp_cap >= TEMPT_SENSOR_ERR_VALUE);

    else
        _IsFault = 0;

    _IsNormal = (delta <= (TEMPT_SENSOR_ERR_MCU - 50)) && (vfd.ad.temp_cap <= TEMPT_RESUME_VALUE);

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_Temp2_SensorFailed(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    int delta = abs(vfd.ad.temp_igbt - vfd.ad.temp_mcu);

    if ((adon_tick <= 500))
        _IsFault = (delta >= TEMPT_SENSOR_ERR_MCU) || (vfd.ad.temp_igbt >= TEMPT_SENSOR_ERR_VALUE);

    else
        _IsFault = 0;

    _IsNormal = (delta <= (TEMPT_SENSOR_ERR_MCU - 50)) && (vfd.ad.temp_igbt <= TEMPT_RESUME_VALUE);

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_Temp3_SensorFailed(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    int delta = abs(vfd.ad.temp_inv_dcl - vfd.ad.temp_mcu);

    if ((adon_tick <= 500))
        _IsFault = (delta >= TEMPT_SENSOR_ERR_MCU) || (vfd.ad.temp_inv_dcl >= TEMPT_SENSOR_ERR_VALUE);

    else
        _IsFault = 0;

    _IsNormal = (delta <= (TEMPT_SENSOR_ERR_MCU - 50)) && (vfd.ad.temp_inv_dcl <= TEMPT_RESUME_VALUE);

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_Temp4_SensorFailed(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////

    _IsFault = 0;
    _IsNormal = !_IsFault;
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}



/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_hdc1080_SensorFailed(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////

    _IsFault = (vfd.ad.temp_HDC1080 >= 1200) && ((vfd.ad.moisture_HDC1080 == 0) || (vfd.ad.moisture_HDC1080 >= 990));

    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}



/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_AD2_SensorErr(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault, _IsNormal;

    if (!vfd.bit.ad_init)
        return;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault = (adon_tick <= 500)  &&
               (DC5V_Is_Ok && DC15V_Is_Ok)   &&
               !vfd.diag.dc_35     &&
               ((vfd.ad.vbus_inv >= 8000) || ((vfd.ad.vbus_inv <= 500) && (vfd.ad.acVinR >= 500)));


    _IsNormal = ((vfd.ad.vbus_inv > 500) && (vfd.ad.vbus_inv < 8000));;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_AD3_SensorErr(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault, _IsNormal;

    if (!vfd.bit.ad_init)
        return;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault = (adon_tick <= 500)             &&
               (DC5V_Is_Ok && DC15V_Is_Ok)    &&
               (vfd.ad.ac_iout_u >= 500);

    _IsNormal = (vfd.ad.ac_iout_u < 500);

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_AD4_SensorErr(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault, _IsNormal;

    if (!vfd.bit.ad_init)
        return;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault = (adon_tick <= 500)             &&
               (DC5V_Is_Ok && DC15V_Is_Ok)    &&
               (vfd.ad.ac_iout_v >= 500);

    _IsNormal = (vfd.ad.ac_iout_v < 500);

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_AD5_SensorErr(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault, _IsNormal;

    if (!vfd.bit.ad_init)
        return;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = (adon_tick <= 500)             &&
                (DC5V_Is_Ok && DC15V_Is_Ok)    &&
                (vfd.ad.ac_iout_w >= 500);

    _IsNormal = (vfd.ad.ac_iout_w < 500);

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_AD6_SensorErr(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault, _IsNormal;

    if (!vfd.bit.ad_init)
        return;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = (adon_tick <= 500)             &&
                (DC5V_Is_Ok && DC15V_Is_Ok)    &&
                (vfd.acin_absfreq > 10)        &&
                (!VacRin.is_ac_flag)           &&
                ((VacRin._sum < 50) || (VacRin._sum > 300)) && 
                ((fabs(VacRin.in) > 500) || (fabs(VacRin.in < 200)));

    _IsNormal = (vfd.fast_ad.acVinR <= 450) && (vfd.fast_ad.acVinR >= 300);
    _IsNormal |= VFD_INPUT_DC;
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_AD7_SensorErr(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault, _IsNormal;

    if (!vfd.bit.ad_init)
        return;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = (adon_tick <= 500)             &&
                (DC5V_Is_Ok && DC15V_Is_Ok)    &&
                (vfd.acin_absfreq > 10)        &&
                (!VacSin.is_ac_flag)           &&
                ((VacSin._sum < 50) || (VacSin._sum > 300)) && 
                ((fabs(VacSin.in) > 500) || (fabs(VacSin.in < 200)));

    _IsNormal = (vfd.fast_ad.acVinS <= 450) && (vfd.fast_ad.acVinS >= 300);
    _IsNormal |= VFD_INPUT_DC;
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_AD8_SensorErr(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault, _IsNormal;

    if (!vfd.bit.ad_init)
        return;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = 0;

    _IsNormal = (vfd.fast_ad.acVinT <= 450) && (vfd.fast_ad.acVinT >= 300);
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}
/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_SpiFlashMountErr(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  vfd.bit.spiflash_init && !vfd.bit.mount_fs;

    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}

extern float sDPLL3P_GetFreqFoLpf(void);
/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_ACVin_PhaseSeqc(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault = (!vfd.diag.dc_22 && !vfd.diag.dc_23 && !vfd.diag.dc_24) && VFD_INPUT_AC  && (vfd.acin_freq < -25.0f) && (VIN_OK_Fast);

    _IsNormal = (vfd.acin_freq > -20.0f);

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}


/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_ACVin_FreqLow(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  VFD_INPUT_AC  && (fabs(vfd.acin_freq) < 43.0f) && VIN_OK_Fast
                && (!vfd.diag.dc_22 && !vfd.diag.dc_23 && !vfd.diag.dc_24);
    _IsFault &= !vfd.diag.dc_64 && !vfd.diag.dc_65 && !vfd.diag.dc_66;

    _IsNormal = !VFD_INPUT_AC || (fabs(vfd.acin_freq) >= 47)
                || vfd.diag.dc_22 || vfd.diag.dc_23 || vfd.diag.dc_24;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_ACVin_FreqOver(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  VFD_INPUT_AC  && (fabs(vfd.acin_freq) > 65.0f) && VIN_OK_Fast
                && (!vfd.diag.dc_22 && !vfd.diag.dc_23 && !vfd.diag.dc_24);
    _IsFault &= !vfd.diag.dc_64 && !vfd.diag.dc_65 && !vfd.diag.dc_66;

    _IsNormal = !VFD_INPUT_AC || (fabs(vfd.acin_freq) <= 63);

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}

extern uint8_t lose_phase_uvw;
/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_InverterIPM(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////

    _IsFault  = (vfd.invc_err_code == 62) ;

    _IsFault &=  mcsdk.init_flag && vfd.bit.dio_init;
    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

    if (diag_bit)
    {
        hardware_irq_pin.f_tim1_breakin_flag = 0;
        lose_phase_uvw = 0;
    }
}

uint8_t gAdref_CheckOk = 0;
/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_AD_Ref(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;


    _IsFault =   vfd.bit.ad_init && (adon_tick <= 500)  &&
                 ((vfd.fast_ad.dc_3_3V <= 2.3f) || (vfd.fast_ad.dc_3_3V >= 4.3f));

    _IsNormal = ((vfd.fast_ad.dc_3_3V >= 3.1f) && (vfd.fast_ad.dc_3_3V <= 3.5f));

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

    if(_IsNormal)
        gAdref_CheckOk = 1;
}



/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_Com485Timeout(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;
    static uint8_t flag = 0;

    if (com_485.flag_normal)
        flag = 1;

    _IsFault =  flag && com_485.flag_timeout ;

    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_ComCanTimeout(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;
    static uint8_t flag = 0;

    if (com_can.flag_normal)
        flag = 1;

    _IsFault =  flag && com_can.flag_timeout ;

    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}


/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_POW(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    static uint32_t activeCnt = 0;
    ///////////////////////////////////////////////////////////////////////////////////

    if (DIO_NAME_READ_BIT("POW") == 0)
        activeCnt++;
    else
        activeCnt = 0;

    _IsFault =  hardware_irq_pin.POW_flag && (activeCnt <= 6000);

    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

    if (diag_bit)
        hardware_irq_pin.POW_flag = 0;

}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_MC_FOC_DURATION(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  0;

    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}


/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_MC_OVER_VOLT(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  =  0;//mcsdk.OccurredFault & MC_OVER_VOLT;

    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_MC_UNDER_VOLT(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  0;

    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_MC_OVER_TEMP(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  0;

    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_MC_START_UP(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  0;

    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_MC_SPEED_FDBK(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  0;

    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_MC_BREAK_IN(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  0;

    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_MC_SW_ERROR(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  0;

    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}

extern int16_t motor_reset_err_occurred(void);
extern uint8_t lose_phase_uvw;
/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_ACIout_LosePhaseU(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  0;
    //_IsFault =  0;
    if (lose_phase_uvw == 1)
    {
        _IsFault = 1;
        faultCnt = diag_table[offset].happen_cnt;
        normalCnt = 0;
    }


    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);
    if (diag_bit)
    {
        lose_phase_uvw = 0;
        motor_reset_err_occurred();
    }
}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_ACIout_LosePhaseV(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  0;
    //_IsFault =   0;
    if (lose_phase_uvw == 2)
    {
        _IsFault = 1;
        faultCnt = diag_table[offset].happen_cnt;
        normalCnt = 0;
    }

    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);
    if (diag_bit)
    {
        lose_phase_uvw = 0;
        motor_reset_err_occurred();
    }
}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_ACIout_LosePhaseW(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  0;

    if (lose_phase_uvw == 3)
    {
        _IsFault = 1;
        faultCnt = diag_table[offset].happen_cnt;
        normalCnt = 0;
    }

    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);
    if (diag_bit)
    {
        lose_phase_uvw = 0;
        motor_reset_err_occurred();
    }
}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_Lock_Stop(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  vfd.bit.lock_stop;
    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_INVCE_Errcode(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  !vfd.bit.hard_stop && (mcsdk.error_code != 0);      //
    _IsNormal = (mcsdk.error_code == 0);

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}


/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_Opencircuit(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    float iout_avr = (vfd.fast_ad.ac_iout_u + vfd.fast_ad.ac_iout_v) / 2;
    static uint16_t od_cnt = 0;
    static uint8_t od_flag = 0;

//    if(INVERTER_IS_RUN && (mcsdk.torque_max) && (mcsdk.Motorfreq <= 1000) && (iout_avr < 0.5f))
//    {
//        od_flag = 1;
//        if(od_cnt < 100)
//            od_cnt++;
//    }
//    else if( !INVERTER_IS_RUN     ||
//        (mcsdk.Motorfreq >= 3000) ||
//        ((vfd.filter_ad.ac_iout_u > 1.0f) && (vfd.filter_ad.ac_iout_v > 1.0f)))
//    {
//        if(od_cnt > 0)
//            od_cnt--;
//        else
//            od_flag = 0;
//    }

    if (INVERTER_IS_RUN
            && (motor_get_accDec_status() != 2)
            && (mcsdk.Motorfreq >= 5000) && (iout_avr <= 0.10f))
        od_flag = 1;
    else
        od_flag = 0;

    static uint16_t load_lost_cnt = 0;
    static uint16_t load_resume_cnt = 0;
    if (INVERTER_IS_RUN
            && (mcsdk.Motorfreq >= 5000)
            && (motor_get_accDec_status() != 2)
            &&
            (((vfd.fast_ad.ac_iout_u <= 0.15f) && (vfd.fast_ad.ac_iout_v <= 0.5f))
             || ((vfd.fast_ad.ac_iout_v <= 0.15f) && (vfd.fast_ad.ac_iout_u <= 0.5f)))
       )
    {
        load_lost_cnt++;
        load_resume_cnt = 0;
    }
    else if ((vfd.filter_ad.ac_iout_u >= 0.4f) || (vfd.filter_ad.ac_iout_v >= 0.4f))
    {
        if (load_resume_cnt <= 100)  load_resume_cnt++;
        else    load_lost_cnt = 0;
    }
    else if ((!INVERTER_IS_RUN) || (mcsdk.Motorfreq < 5000))   load_lost_cnt = 0;

    if (load_lost_cnt >= 20)   od_flag = 1;

    _IsFault  =  INVERTER_IS_RUN && od_flag;
    _IsNormal = !INVERTER_IS_RUN || (!mcsdk.torque_max) || (mcsdk.Motorfreq >= mcsdk.MotorfreqRef);

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);
    if (diag_bit)
    {
        load_lost_cnt = 0;
        load_resume_cnt = 0;
    }

}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_OverTempt_MCU_Warn(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = (vfd.filter_ad.temp_mcu >= 100) || ((vfd.ad.temp_HDC1080 >= 1000) && !vfd.diag.dc_81);
    _IsNormal = (vfd.filter_ad.temp_mcu <= 85) && (vfd.ad.temp_HDC1080 <= 850);

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_OutOverLoad(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    static uint32_t over_cnter = 0;
    uint32_t timeout_cnt = 60000;
    float power = 0;
    float power_base = 0;

    power_base = (float)funcCode.code.motorParaM1.elem.ratingCurrent / 100;//
    power = (vfd.filter_ad.ac_iout_u + vfd.filter_ad.ac_iout_v) / 2;

    if (!INVERTER_IS_RUN)
    {
        over_cnter = 0;
    }

    if (power >= (power_base * 1.1f))
    {
        over_cnter++;
    }
    else if (power <= (power_base * 1.0f))
    {
        if (over_cnter)  over_cnter--;
    }

    if (power >= (power_base * 1.9f))      timeout_cnt = 100;
    else if (power >= (power_base * 1.8f)) timeout_cnt = 500;
    else if (power >= (power_base * 1.7f)) timeout_cnt = 1500;
    else if (power >= (power_base * 1.6f)) timeout_cnt = 4500;
    else if (power >= (power_base * 1.5f)) timeout_cnt = 9000;
    else if (power >= (power_base * 1.4f)) timeout_cnt = 15000;
    else if (power >= (power_base * 1.3f)) timeout_cnt = 30000;
    else if (power >= (power_base * 1.2f)) timeout_cnt = 45000;
    else if (power >= (power_base * 1.1f))  timeout_cnt = 60000;
    else    timeout_cnt = 60000;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault = (over_cnter >= timeout_cnt);

    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);

}


#define IOUT_OVERLOAD_BASE      ((float)funcCode.code.motorParaM1.elem.ratingCurrent/100)//(10.85f)

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_ACIout_Overload110(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    if (!vfd.bit.ad_init)
        return;

    float power = 0;

    power = (vfd.filter_ad.ac_iout_u > vfd.filter_ad.ac_iout_v) ? vfd.filter_ad.ac_iout_u : vfd.filter_ad.ac_iout_v;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = (power >= (IOUT_OVERLOAD_BASE * 1.1f));
    _IsFault  &= INVERTER_IS_RUN;
    
    _IsNormal = (power <= IOUT_OVERLOAD_BASE);


    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);
}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_ACIout_Overload120(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    if (!vfd.bit.ad_init)
        return;

    float power = 0;

    power = (vfd.filter_ad.ac_iout_u > vfd.filter_ad.ac_iout_v) ? vfd.filter_ad.ac_iout_u : vfd.filter_ad.ac_iout_v;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = (power >= (IOUT_OVERLOAD_BASE * 1.2f));
    _IsFault  &= INVERTER_IS_RUN;
    _IsNormal = (power <= IOUT_OVERLOAD_BASE);


    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);
}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_ACIout_Overload130(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    if (!vfd.bit.ad_init)
        return;

    float power = 0;

    power = (vfd.filter_ad.ac_iout_u > vfd.filter_ad.ac_iout_v) ? vfd.filter_ad.ac_iout_u : vfd.filter_ad.ac_iout_v;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = (power >= (IOUT_OVERLOAD_BASE * 1.3f));
    _IsFault  &= INVERTER_IS_RUN;
    _IsNormal = (power <= IOUT_OVERLOAD_BASE);


    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);
}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_ACIout_Overload140(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    if (!vfd.bit.ad_init)
        return;

    float power = 0;

    power = (vfd.filter_ad.ac_iout_u > vfd.filter_ad.ac_iout_v) ? vfd.filter_ad.ac_iout_u : vfd.filter_ad.ac_iout_v;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = (power >= (IOUT_OVERLOAD_BASE * 1.4f));
    _IsFault  &= INVERTER_IS_RUN;
    _IsNormal = (power <= IOUT_OVERLOAD_BASE);


    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);
}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_ACIout_Overload150(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    if (!vfd.bit.ad_init)
        return;

    float power = 0;

    power = (vfd.filter_ad.ac_iout_u > vfd.filter_ad.ac_iout_v) ? vfd.filter_ad.ac_iout_u : vfd.filter_ad.ac_iout_v;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = (power >= (IOUT_OVERLOAD_BASE * 1.5f));
    _IsFault  &= INVERTER_IS_RUN;
    _IsNormal = (power <= IOUT_OVERLOAD_BASE);


    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);
}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_ACIout_Overload160(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    if (!vfd.bit.ad_init)
        return;

    float power = 0;

    power = (vfd.filter_ad.ac_iout_u > vfd.filter_ad.ac_iout_v) ? vfd.filter_ad.ac_iout_u : vfd.filter_ad.ac_iout_v;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = (power >= (IOUT_OVERLOAD_BASE * 1.6f));
    _IsFault  &= INVERTER_IS_RUN;
    _IsNormal = (power <= IOUT_OVERLOAD_BASE);


    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);
}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_ACIout_Overload170(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    if (!vfd.bit.ad_init)
        return;

    float power = 0;

    power = (vfd.filter_ad.ac_iout_u > vfd.filter_ad.ac_iout_v) ? vfd.filter_ad.ac_iout_u : vfd.filter_ad.ac_iout_v;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = (power >= (IOUT_OVERLOAD_BASE * 1.7f));
    _IsFault  &= INVERTER_IS_RUN;
    _IsNormal = (power <= IOUT_OVERLOAD_BASE);


    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);
}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_ACIout_Overload180(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    if (!vfd.bit.ad_init)
        return;

    float power = 0;

    power = (vfd.filter_ad.ac_iout_u > vfd.filter_ad.ac_iout_v) ? vfd.filter_ad.ac_iout_u : vfd.filter_ad.ac_iout_v;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = (power >= (IOUT_OVERLOAD_BASE * 1.8f));
    _IsFault  &= INVERTER_IS_RUN;
    _IsNormal = (power <= IOUT_OVERLOAD_BASE);


    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);
}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_ACIout_Overload190(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault = 0, _IsNormal;

    if (!vfd.bit.ad_init)
        return;

    float power = 0;

    power = (vfd.filter_ad.ac_iout_u > vfd.filter_ad.ac_iout_v) ? vfd.filter_ad.ac_iout_u : vfd.filter_ad.ac_iout_v;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = (power >= (IOUT_OVERLOAD_BASE * 1.9f));
    _IsFault  &= INVERTER_IS_RUN;
    _IsNormal = (power <= IOUT_OVERLOAD_BASE);


    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);
}


/**
  * @brief
  * @param
  * @retval
  */

void diag_logic_all(void)
{
    if (vfd.bit.ad_init && (DC5V_Is_Ok && DC15V_Is_Ok))
        adon_tick++;

    DiagLgc_VoltInError((uint8_t *)&vfd.diag,       44);

    if (VFD_INPUT_24V)
    {
        vfd.bit.novolt_warning_flag = 0;
        diag_lockstop_reset();
        return;
    }

    if (!vfd.bit.lock_stop)
    {
        DiagLgc_AD_Ref(&vfd.diag,  35);

        if (!vfd.diag.dc_35   &&
                (vfd.bit.io_init || (rt_tick_get() > 5 * RT_TICK_PER_SECOND))
           )
        {
            DiagLgc_OverTempt_1_Warn((uint8_t *)&vfd.diag,       3);
            DiagLgc_OverTempt_2_Warn((uint8_t *)&vfd.diag,       4);
            DiagLgc_OverTempt_3_Warn((uint8_t *)&vfd.diag,       5);
            DiagLgc_OverMoisture((uint8_t *)&vfd.diag,       7);
            DiagLgc_InverterIPM((uint8_t *)&vfd.diag,        8);

            DiagLgc_ACVin_Over((uint8_t *)&vfd.diag,        11);
            DiagLgc_ACVin_Low((uint8_t *)&vfd.diag,         12);
            DiagLgc_Vbus_Over1((uint8_t *)&vfd.diag,        13);
            DiagLgc_Vbus_Over2((uint8_t *)&vfd.diag,        14);
            DiagLgc_Vbus_Low((uint8_t *)&vfd.diag,          15);

            DiagLgc_DCVin_Over((uint8_t *)&vfd.diag,        20);
            DiagLgc_DCVin_Low((uint8_t *)&vfd.diag,         21);
            DiagLgc_ACVin_LosePhaseR((uint8_t *)&vfd.diag,  22);
            DiagLgc_ACVin_LosePhaseS((uint8_t *)&vfd.diag,  23);
            DiagLgc_ACVin_LosePhaseT((uint8_t *)&vfd.diag,  24);
            DiagLgc_ACIout_LosePhaseU((uint8_t *)&vfd.diag, 25);
            DiagLgc_ACIout_LosePhaseV((uint8_t *)&vfd.diag, 26);
            DiagLgc_ACIout_LosePhaseW((uint8_t *)&vfd.diag, 27);
            DiagLgc_Vbus_KMON((uint8_t *)&vfd.diag,         28);
            DiagLgc_ACVin_PhaseSeqc((uint8_t *)&vfd.diag,   31);
            DiagLgc_ACVin_FreqLow((uint8_t *)&vfd.diag,     32);
            DiagLgc_ACVin_FreqOver((uint8_t *)&vfd.diag,    33);
            DiagLgc_SpiFlashMountErr((uint8_t *)&vfd.diag,  34);
            DiagLgc_MainPowerMisWire((uint8_t *)&vfd.diag,  40);

            //DiagLgc_dc24v((uint8_t *)&vfd.diag,           45);
            DiagLgc_dcInputPhaseErr((uint8_t *)&vfd.diag,   46);
            DiagLgc_Com485Timeout((uint8_t *)&vfd.diag,     47);
            DiagLgc_ComCanTimeout((uint8_t *)&vfd.diag,     48);
            DiagLgc_dcInputLineErr((uint8_t *)&vfd.diag,    49);
            DiagLgc_OverTempt_5_Warn((uint8_t *)&vfd.diag,  50);

            DiagLgc_IPM_ResetError((uint8_t *)&vfd.diag,   51);

            DiagLgc_AD2_SensorErr((uint8_t *)&vfd.diag,     60);
            DiagLgc_AD3_SensorErr((uint8_t *)&vfd.diag,     61);
            DiagLgc_AD4_SensorErr((uint8_t *)&vfd.diag,     62);
            DiagLgc_AD5_SensorErr((uint8_t *)&vfd.diag,     63);
            DiagLgc_AD6_SensorErr((uint8_t *)&vfd.diag,     64);
            DiagLgc_AD7_SensorErr((uint8_t *)&vfd.diag,     65);
            DiagLgc_AD8_SensorErr((uint8_t *)&vfd.diag,     66);

            DiagLgc_Temp1_SensorFailed((uint8_t *)&vfd.diag, 76);
            DiagLgc_Temp2_SensorFailed((uint8_t *)&vfd.diag, 77);
            DiagLgc_Temp3_SensorFailed((uint8_t *)&vfd.diag, 78);

            DiagLgc_hdc1080_SensorFailed((uint8_t *)&vfd.diag, 81);

            DiagLgc_OverTempt_1_Stop((uint8_t *)&vfd.diag,   82);
            DiagLgc_OverTempt_2_Stop((uint8_t *)&vfd.diag,   83);
            DiagLgc_OverTempt_3_Stop((uint8_t *)&vfd.diag,   84);
            DiagLgc_INVCE_Errcode((uint8_t *)&vfd.diag,      87);
            DiagLgc_OverTempt_MCU_Warn((uint8_t *)&vfd.diag, 89);
            //DiagLgc_OutOverLoad((uint8_t *)&vfd.diag,        90);
            DiagLgc_dc92_dc114();
            DiagLgc_ACIout_Overload110((uint8_t *)&vfd.diag, 114);
            DiagLgc_ACIout_Overload120((uint8_t *)&vfd.diag, 115);
            DiagLgc_ACIout_Overload130((uint8_t *)&vfd.diag, 116);
            DiagLgc_ACIout_Overload140((uint8_t *)&vfd.diag, 117);
            DiagLgc_ACIout_Overload150((uint8_t *)&vfd.diag, 118);
            DiagLgc_ACIout_Overload160((uint8_t *)&vfd.diag, 119);
            DiagLgc_ACIout_Overload170((uint8_t *)&vfd.diag, 120);
            DiagLgc_ACIout_Overload180((uint8_t *)&vfd.diag, 121);
            DiagLgc_ACIout_Overload190((uint8_t *)&vfd.diag, 122);
            DiagLgc_AD9_SensorErr((uint8_t *)&vfd.diag, 123);
            DiagLgc_AD10_SensorErr((uint8_t *)&vfd.diag, 124);
            DiagLgc_AD11_SensorErr((uint8_t *)&vfd.diag, 125);

        }


        DiagLgc_12V_Over((uint8_t *)&vfd.diag,       36);
        DiagLgc_12V_Low((uint8_t *)&vfd.diag,        37);
        DiagLgc_5V_Over((uint8_t *)&vfd.diag,       38);
        DiagLgc_5V_Low((uint8_t *)&vfd.diag,        39);
    }
    else
    {
        motor_reset_err_occurred(); //lockstop,reset low level motordriver err bit and err code
    }

    DiagLgc_Lock_Stop((uint8_t *)&vfd.diag,   86);
}

/**
  * @brief 输出电压U相传感器故障检测
  * @param diag 诊断数据指针
  * @param offset 诊断表偏移量
  * @retval None
  */
void DiagLgc_AD9_SensorErr(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault, _IsNormal;

    if (!vfd.bit.ad_init)
        return;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = (adon_tick <= 500)             &&
                (DC5V_Is_Ok && DC15V_Is_Ok)    &&
                ((VacUout._sum < 50) || (VacUout._sum > 300)) && 
                (fabs(VacUout.in) > 500);

    _IsNormal = ((VacUout._sum > 50) && (VacUout._sum < 300));

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);
}

/**
  * @brief 输出电压V相传感器故障检测
  * @param diag 诊断数据指针
  * @param offset 诊断表偏移量
  * @retval None
  */
void DiagLgc_AD10_SensorErr(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault, _IsNormal;

    if (!vfd.bit.ad_init)
        return;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = (adon_tick <= 500)             &&
                (DC5V_Is_Ok && DC15V_Is_Ok)    &&
                ((VacVout._sum < 50) || (VacVout._sum > 300)) && 
                (fabs(VacVout.in) > 500);

    _IsNormal = ((VacVout._sum > 50) && (VacVout._sum < 300));

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);
}

/**
  * @brief 输出电压W相传感器故障检测
  * @param diag 诊断数据指针
  * @param offset 诊断表偏移量
  * @retval None
  */
void DiagLgc_AD11_SensorErr(uint8_t *diag, int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag, offset);
    uint8_t _IsFault, _IsNormal;

    if (!vfd.bit.ad_init)
        return;

    ///////////////////////////////////////////////////////////////////////////////////
//    _IsFault  = (adon_tick <= 500)             &&
//                (DC5V_Is_Ok && DC15V_Is_Ok)    &&
//                ((VacWout._sum < 50) || (VacWout._sum > 300)) && 
//                ((fabs(VacWout.in) > 500) || (fabs(VacWout.in < 200)));
    _IsFault  = 0;
    
    _IsNormal = ((VacWout._sum > 50) && (VacWout._sum < 300));
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                    _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                    _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag, offset, diag_bit);
}