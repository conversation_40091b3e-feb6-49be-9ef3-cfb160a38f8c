<?xml version="1.0" encoding="UTF-8"?>
<project_config>
    <paths>
        <!-- bootloader hex文件完整路径 -->
        <bootloader_hex>../../../../build/PMSM12_Boot_A01_V.hex</bootloader_hex>

        <!-- main hex文件完整路径 -->
        <main_hex>../../../../build/inv/</main_hex>

        <!-- main bin文件完整路径 -->
        <main_bin>../../../../build/bin/</main_bin>

        <!-- 输出目录路径 -->
        <output_dir>../../../../build</output_dir>
        
        <!-- STM32库文件路径 -->
        <stm32_libraries>../libraries</stm32_libraries>
        
        <!-- 启动文件路径（包含版本号定义的.s文件）,用于获取版本号 -->
        <startup_file>../libraries/STM32G4xx_HAL/CMSIS/Device/ST/STM32G4xx/Source/Templates/arm/startup_stm32g474xx.s</startup_file>
        
        <!-- Keil工程文件路径 -->
        <keil_uvoptx>vfd.uvoptx</keil_uvoptx>
        <keil_uvprojx>vfd.uvprojx</keil_uvprojx>
    </paths>
    
    <settings>
        <!-- STM32型号，如果不指定则从路径自动提取 -->
        <stm32_model></stm32_model>

        <!-- 项目名称，必须配置 -->
        <project_name>PMSM12</project_name>

        <!-- 版本号，如果不指定则从启动文件提取 -->
        <version></version>

        <!-- 输出目录前缀，默认为burnprocess_py -->
        <output_prefix>PMSM12_Firmware</output_prefix>
    </settings>

    <!-- 工程发布配置：支持多个工程的发布文件名配置 -->
    <project_releases>
        <!-- 工程1：PFC工程 -->
        <project>
            <target_name>发行运营</target_name>
            <release_hex_name>H0584813_APP_PMSM12</release_hex_name>
            <release_bin_name>H0584813_APP_PMSM12</release_bin_name>
        </project>


        <!-- 工程2：示例工程（可以添加更多工程配置） -->
        <!--
        <project>
            <target_name>inverter</target_name>
            <release_hex_name>H0582249_APP_PVPB02_INVERTER</release_hex_name>
            <release_bin_name>H0582249_APP_PVPB02_INVERTER</release_bin_name>
        </project>
        -->
    </project_releases>
</project_config>







