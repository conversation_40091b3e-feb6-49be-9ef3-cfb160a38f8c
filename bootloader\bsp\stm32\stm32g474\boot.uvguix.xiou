<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<ProjectGui xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_guix.xsd">

  <SchemaVersion>-6.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <PrjGuiSettings>
    <LastAddFilePath></LastAddFilePath>
  </PrjGuiSettings>

  <ViewPool/>

  <SECTreeCtrl>
    <View>
      <WinId>38003</WinId>
      <ViewName>Registers</ViewName>
      <TableColWidths>187 246</TableColWidths>
    </View>
    <View>
      <WinId>346</WinId>
      <ViewName>Code Coverage</ViewName>
      <TableColWidths>1404 822</TableColWidths>
    </View>
    <View>
      <WinId>204</WinId>
      <ViewName>Performance Analyzer</ViewName>
      <TableColWidths>1564 234 234 662</TableColWidths>
    </View>
  </SECTreeCtrl>

  <TreeListPane>
    <View>
      <WinId>35141</WinId>
      <ViewName>Event Statistics</ViewName>
      <UserString></UserString>
      <TableColWidths>200 50 700</TableColWidths>
    </View>
    <View>
      <WinId>1506</WinId>
      <ViewName>Symbols</ViewName>
      <UserString></UserString>
      <TableColWidths>106 106 106</TableColWidths>
    </View>
    <View>
      <WinId>1936</WinId>
      <ViewName>Watch 1</ViewName>
      <UserString></UserString>
      <TableColWidths>200 133 133</TableColWidths>
    </View>
    <View>
      <WinId>1937</WinId>
      <ViewName>Watch 2</ViewName>
      <UserString></UserString>
      <TableColWidths>200 133 133</TableColWidths>
    </View>
    <View>
      <WinId>1935</WinId>
      <ViewName>Call Stack + Locals</ViewName>
      <UserString></UserString>
      <TableColWidths>200 133 133</TableColWidths>
    </View>
    <View>
      <WinId>2506</WinId>
      <ViewName>Trace Data</ViewName>
      <UserString></UserString>
      <TableColWidths>75 135 130 95 70 230 200 150</TableColWidths>
    </View>
    <View>
      <WinId>466</WinId>
      <ViewName>Source Browser</ViewName>
      <UserString>500</UserString>
      <TableColWidths>300</TableColWidths>
    </View>
  </TreeListPane>

  <CompViewPool/>

  <WindowSettings>
    <LogicAnalizer>
      <ShowLACursor>0</ShowLACursor>
      <ShowSignalInfo>0</ShowSignalInfo>
      <ShowCycles>0</ShowCycles>
      <LeftSideBarSize>50</LeftSideBarSize>
      <TimeBaseIndex>16</TimeBaseIndex>
    </LogicAnalizer>
  </WindowSettings>

  <WinLayoutEx>
    <sActiveDebugView></sActiveDebugView>
    <WindowPosition>
      <length>44</length>
      <flags>2</flags>
      <showCmd>3</showCmd>
      <MinPosition>
        <xPos>-32000</xPos>
        <yPos>-32000</yPos>
      </MinPosition>
      <MaxPosition>
        <xPos>-1</xPos>
        <yPos>-1</yPos>
      </MaxPosition>
      <NormalPosition>
        <Top>0</Top>
        <Left>-11</Left>
        <Right>1950</Right>
        <Bottom>1166</Bottom>
      </NormalPosition>
    </WindowPosition>
    <MDIClientArea>
      <RegID>0</RegID>
      <MDITabState>
        <Len>1527</Len>
        <Data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ata>
      </MDITabState>
    </MDIClientArea>
  </WinLayoutEx>

</ProjectGui>
