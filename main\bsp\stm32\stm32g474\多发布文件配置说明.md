# BIN_HEX_PY 多发布文件配置说明

## 功能概述

现在 `BIN_HEX_PY.py` 脚本支持为一个 `target_name` 配置多个发布文件，可以同时生成多个不同名称的 HEX 和 BIN 文件。

## 配置文件结构

### 新格式（推荐）

```xml
<project>
    <target_name>发行运营</target_name>
    <releases>
        <!-- 发布文件1：正式版本 -->
        <release>
            <name>H0584813_APP_PMSM12</name>
            <hex_name>H0584813_APP_PMSM12</hex_name>
            <bin_name>H0584813_APP_PMSM12</bin_name>
        </release>
        <!-- 发布文件2：调试版本 -->
        <release>
            <name>H0584813_APP_PMSM12_DEBUG</name>
            <hex_name>H0584813_APP_PMSM12_DEBUG</hex_name>
            <bin_name>H0584813_APP_PMSM12_DEBUG</bin_name>
        </release>
        <!-- 发布文件3：测试版本 -->
        <release>
            <name>H0584813_APP_PMSM12_TEST</name>
            <hex_name>H0584813_APP_PMSM12_TEST</hex_name>
            <bin_name>H0584813_APP_PMSM12_TEST</bin_name>
        </release>
    </releases>
</project>
```

### 旧格式（兼容）

```xml
<project>
    <target_name>发行运营</target_name>
    <release_hex_name>H0584813_APP_PMSM12</release_hex_name>
    <release_bin_name>H0584813_APP_PMSM12</release_bin_name>
</project>
```

## 配置字段说明

### release 节点字段

- `name`: 发布配置的描述名称（用于日志显示）
- `hex_name`: HEX 文件的基础名称（不包含版本号和扩展名）
- `bin_name`: BIN 文件的基础名称（不包含版本号和扩展名）

### 生成的文件名格式

- HEX 文件：`{hex_name}_{version}_BSM.hex`
- BIN 文件：`{bin_name}_{version}_BSM.bin`

其中 `{version}` 格式为 `V{major}-{minor}`，例如：`V1-00`

## 使用示例

### 示例1：单个发布文件

```xml
<project>
    <target_name>发行运营</target_name>
    <releases>
        <release>
            <name>正式版本</name>
            <hex_name>H0584813_APP_PMSM12</hex_name>
            <bin_name>H0584813_APP_PMSM12</bin_name>
        </release>
    </releases>
</project>
```

生成文件：
- `H0584813_APP_PMSM12_V1-00_BSM.hex`
- `H0584813_APP_PMSM12_V1-00_BSM.bin`

### 示例2：多个发布文件

```xml
<project>
    <target_name>发行运营</target_name>
    <releases>
        <release>
            <name>正式版本</name>
            <hex_name>H0584813_APP_PMSM12</hex_name>
            <bin_name>H0584813_APP_PMSM12</bin_name>
        </release>
        <release>
            <name>调试版本</name>
            <hex_name>H0584813_APP_PMSM12_DEBUG</hex_name>
            <bin_name>H0584813_APP_PMSM12_DEBUG</bin_name>
        </release>
        <release>
            <name>测试版本</name>
            <hex_name>H0584813_APP_PMSM12_TEST</hex_name>
            <bin_name>H0584813_APP_PMSM12_TEST</bin_name>
        </release>
    </releases>
</project>
```

生成文件：
- `H0584813_APP_PMSM12_V1-00_BSM.hex`
- `H0584813_APP_PMSM12_V1-00_BSM.bin`
- `H0584813_APP_PMSM12_DEBUG_V1-00_BSM.hex`
- `H0584813_APP_PMSM12_DEBUG_V1-00_BSM.bin`
- `H0584813_APP_PMSM12_TEST_V1-00_BSM.hex`
- `H0584813_APP_PMSM12_TEST_V1-00_BSM.bin`

## 脚本输出示例

```
找到 3 个发布配置 (目标工程: 发行运营)
  处理发布配置 1: 正式版本
    创建发布HEX文件: H0584813_APP_PMSM12_V1-00_BSM.hex
    创建发布BIN文件: H0584813_APP_PMSM12_V1-00_BSM.bin
  处理发布配置 2: 调试版本
    创建发布HEX文件: H0584813_APP_PMSM12_DEBUG_V1-00_BSM.hex
    创建发布BIN文件: H0584813_APP_PMSM12_DEBUG_V1-00_BSM.bin
  处理发布配置 3: 测试版本
    创建发布HEX文件: H0584813_APP_PMSM12_TEST_V1-00_BSM.hex
    创建发布BIN文件: H0584813_APP_PMSM12_TEST_V1-00_BSM.bin
```

## 注意事项

1. **向后兼容**：脚本完全兼容旧的配置格式
2. **灵活配置**：可以只配置 HEX 或只配置 BIN，留空即可跳过
3. **多工程支持**：不同的 `target_name` 可以有不同的发布配置
4. **版本号自动格式化**：版本号会自动格式化为 `V{major}-{minor}` 格式

## 启用多发布文件

要启用多个发布文件，只需在配置文件中取消相应的注释即可：

```xml
<!-- 取消注释以启用调试版本 -->
<release>
    <name>H0584813_APP_PMSM12_DEBUG</name>
    <hex_name>H0584813_APP_PMSM12_DEBUG</hex_name>
    <bin_name>H0584813_APP_PMSM12_DEBUG</bin_name>
</release>
```
